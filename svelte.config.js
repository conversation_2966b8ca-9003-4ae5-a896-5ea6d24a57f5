import adapter from '@sveltejs/adapter-static';
import { vitePreprocess } from '@sveltejs/vite-plugin-svelte';

/** @type {import('@sveltejs/kit').Config} */
const config = {
	preprocess: vitePreprocess(),

	kit: {
		// Use static adapter for full static site generation
		adapter: adapter({
			// Options for the adapter
			pages: 'build',
			assets: 'build',
			fallback: 'index.html', // SPA fallback
			precompress: false
		}),

		alias: {
			'$lib': './src/lib'
		},
	},

	vitePlugin: {
		inspector: {
			holdMode: false,
			showToggleButton: 'always',
			toggleButtonPos: 'bottom-right'
		}
	}
};

export default config;
