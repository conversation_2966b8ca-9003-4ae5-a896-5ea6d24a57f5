{"models": {"_http_response": {"id": "net._http_response", "schemaName": "net", "tableName": "_http_response", "fields": [{"id": "net._http_response.id", "name": "id", "columnName": "id", "type": "int8", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "net._http_response.status_code", "name": "status_code", "columnName": "status_code", "type": "int4", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "net._http_response.content_type", "name": "content_type", "columnName": "content_type", "type": "text", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "net._http_response.headers", "name": "headers", "columnName": "headers", "type": "jsonb", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "net._http_response.content", "name": "content", "columnName": "content", "type": "text", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "net._http_response.timed_out", "name": "timed_out", "columnName": "timed_out", "type": "bool", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "net._http_response.error_msg", "name": "error_msg", "columnName": "error_msg", "type": "text", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "net._http_response.created", "name": "created", "columnName": "created", "type": "timestamptz", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false, "maxLength": null}], "uniqueConstraints": []}, "audit_log_entries": {"id": "auth.audit_log_entries", "schemaName": "auth", "tableName": "audit_log_entries", "fields": [{"id": "auth.audit_log_entries.instance_id", "name": "instance_id", "columnName": "instance_id", "type": "uuid", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.audit_log_entries.id", "name": "id", "columnName": "id", "type": "uuid", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": true, "maxLength": null}, {"id": "auth.audit_log_entries.payload", "name": "payload", "columnName": "payload", "type": "json", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.audit_log_entries.created_at", "name": "created_at", "columnName": "created_at", "type": "timestamptz", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.audit_log_entries.ip_address", "name": "ip_address", "columnName": "ip_address", "type": "<PERSON><PERSON><PERSON>", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false, "maxLength": 64}], "uniqueConstraints": []}, "buckets": {"id": "storage.buckets", "schemaName": "storage", "tableName": "buckets", "fields": [{"id": "storage.buckets.id", "name": "id", "columnName": "id", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": true, "maxLength": null}, {"id": "storage.buckets.name", "name": "name", "columnName": "name", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "storage.buckets.owner", "name": "owner", "columnName": "owner", "type": "uuid", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "storage.buckets.created_at", "name": "created_at", "columnName": "created_at", "type": "timestamptz", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false, "maxLength": null}, {"id": "storage.buckets.updated_at", "name": "updated_at", "columnName": "updated_at", "type": "timestamptz", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false, "maxLength": null}, {"id": "storage.buckets.public", "name": "public", "columnName": "public", "type": "bool", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false, "maxLength": null}, {"id": "storage.buckets.avif_autodetection", "name": "avif_autodetection", "columnName": "avif_autodetection", "type": "bool", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false, "maxLength": null}, {"id": "storage.buckets.file_size_limit", "name": "file_size_limit", "columnName": "file_size_limit", "type": "int8", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "storage.buckets.allowed_mime_types", "name": "allowed_mime_types", "columnName": "allowed_mime_types", "type": "text[]", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "storage.buckets.owner_id", "name": "owner_id", "columnName": "owner_id", "type": "text", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"name": "objects", "type": "objects", "isRequired": false, "kind": "object", "relationName": "objectsTobuckets", "relationFromFields": [], "relationToFields": [], "isList": true, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}, {"name": "prefixes", "type": "prefixes", "isRequired": false, "kind": "object", "relationName": "prefixesTobuckets", "relationFromFields": [], "relationToFields": [], "isList": true, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}, {"name": "s3_multipart_uploads", "type": "s3_multipart_uploads", "isRequired": false, "kind": "object", "relationName": "s3_multipart_uploadsTobuckets", "relationFromFields": [], "relationToFields": [], "isList": true, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}, {"name": "s3_multipart_uploads_parts", "type": "s3_multipart_uploads_parts", "isRequired": false, "kind": "object", "relationName": "s3_multipart_uploads_partsTobuckets", "relationFromFields": [], "relationToFields": [], "isList": true, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}], "uniqueConstraints": [{"name": "bname", "fields": ["name"], "nullNotDistinct": false}]}, "country": {"id": "public.country", "schemaName": "public", "tableName": "country", "fields": [{"id": "public.country.code", "name": "code", "columnName": "code", "type": "bpchar", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": true, "maxLength": 2}, {"id": "public.country.name", "name": "name", "columnName": "name", "type": "<PERSON><PERSON><PERSON>", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": 50}, {"name": "organization", "type": "organization", "isRequired": false, "kind": "object", "relationName": "organizationTocountry", "relationFromFields": [], "relationToFields": [], "isList": true, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}], "uniqueConstraints": [{"name": "country_name_key", "fields": ["name"], "nullNotDistinct": false}, {"name": "country_pkey", "fields": ["code"], "nullNotDistinct": false}]}, "flow_state": {"id": "auth.flow_state", "schemaName": "auth", "tableName": "flow_state", "fields": [{"id": "auth.flow_state.id", "name": "id", "columnName": "id", "type": "uuid", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": true, "maxLength": null}, {"id": "auth.flow_state.user_id", "name": "user_id", "columnName": "user_id", "type": "uuid", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.flow_state.auth_code", "name": "auth_code", "columnName": "auth_code", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.flow_state.code_challenge_method", "name": "code_challenge_method", "columnName": "code_challenge_method", "type": "code_challenge_method", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.flow_state.code_challenge", "name": "code_challenge", "columnName": "code_challenge", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.flow_state.provider_type", "name": "provider_type", "columnName": "provider_type", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.flow_state.provider_access_token", "name": "provider_access_token", "columnName": "provider_access_token", "type": "text", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.flow_state.provider_refresh_token", "name": "provider_refresh_token", "columnName": "provider_refresh_token", "type": "text", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.flow_state.created_at", "name": "created_at", "columnName": "created_at", "type": "timestamptz", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.flow_state.updated_at", "name": "updated_at", "columnName": "updated_at", "type": "timestamptz", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.flow_state.authentication_method", "name": "authentication_method", "columnName": "authentication_method", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.flow_state.auth_code_issued_at", "name": "auth_code_issued_at", "columnName": "auth_code_issued_at", "type": "timestamptz", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"name": "saml_relay_states", "type": "saml_relay_states", "isRequired": false, "kind": "object", "relationName": "saml_relay_statesToflow_state", "relationFromFields": [], "relationToFields": [], "isList": true, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}], "uniqueConstraints": []}, "hooks": {"id": "supabase_functions.hooks", "schemaName": "supabase_functions", "tableName": "hooks", "fields": [{"id": "supabase_functions.hooks.id", "name": "id", "columnName": "id", "type": "int8", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": {"identifier": "\"supabase_functions\".\"hooks_id_seq\"", "increment": 1, "start": 1}, "hasDefaultValue": true, "isId": true, "maxLength": null}, {"id": "supabase_functions.hooks.hook_table_id", "name": "hook_table_id", "columnName": "hook_table_id", "type": "int4", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "supabase_functions.hooks.hook_name", "name": "hook_name", "columnName": "hook_name", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "supabase_functions.hooks.created_at", "name": "created_at", "columnName": "created_at", "type": "timestamptz", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false, "maxLength": null}, {"id": "supabase_functions.hooks.request_id", "name": "request_id", "columnName": "request_id", "type": "int8", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}], "uniqueConstraints": [{"name": "hooks_pkey", "fields": ["id"], "nullNotDistinct": false}]}, "http_request_queue": {"id": "net.http_request_queue", "schemaName": "net", "tableName": "http_request_queue", "fields": [{"id": "net.http_request_queue.id", "name": "id", "columnName": "id", "type": "int8", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": {"identifier": "\"net\".\"http_request_queue_id_seq\"", "increment": 1, "start": 1}, "hasDefaultValue": true, "isId": false, "maxLength": null}, {"id": "net.http_request_queue.method", "name": "method", "columnName": "method", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "net.http_request_queue.url", "name": "url", "columnName": "url", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "net.http_request_queue.headers", "name": "headers", "columnName": "headers", "type": "jsonb", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "net.http_request_queue.body", "name": "body", "columnName": "body", "type": "bytea", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "net.http_request_queue.timeout_milliseconds", "name": "timeout_milliseconds", "columnName": "timeout_milliseconds", "type": "int4", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}], "uniqueConstraints": []}, "identities": {"id": "auth.identities", "schemaName": "auth", "tableName": "identities", "fields": [{"id": "auth.identities.provider_id", "name": "provider_id", "columnName": "provider_id", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.identities.user_id", "name": "user_id", "columnName": "user_id", "type": "uuid", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.identities.identity_data", "name": "identity_data", "columnName": "identity_data", "type": "jsonb", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.identities.provider", "name": "provider", "columnName": "provider", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.identities.last_sign_in_at", "name": "last_sign_in_at", "columnName": "last_sign_in_at", "type": "timestamptz", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.identities.created_at", "name": "created_at", "columnName": "created_at", "type": "timestamptz", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.identities.updated_at", "name": "updated_at", "columnName": "updated_at", "type": "timestamptz", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.identities.email", "name": "email", "columnName": "email", "type": "text", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": true, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.identities.id", "name": "id", "columnName": "id", "type": "uuid", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": true, "maxLength": null}, {"name": "users", "type": "users", "isRequired": true, "kind": "object", "relationName": "identitiesTousers", "relationFromFields": ["user_id"], "relationToFields": ["id"], "isList": false, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}], "uniqueConstraints": [{"name": "identities_provider_id_provider_unique", "fields": ["provider", "provider_id"], "nullNotDistinct": false}]}, "instances": {"id": "auth.instances", "schemaName": "auth", "tableName": "instances", "fields": [{"id": "auth.instances.id", "name": "id", "columnName": "id", "type": "uuid", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": true, "maxLength": null}, {"id": "auth.instances.uuid", "name": "uuid", "columnName": "uuid", "type": "uuid", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.instances.raw_base_config", "name": "raw_base_config", "columnName": "raw_base_config", "type": "text", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.instances.created_at", "name": "created_at", "columnName": "created_at", "type": "timestamptz", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.instances.updated_at", "name": "updated_at", "columnName": "updated_at", "type": "timestamptz", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}], "uniqueConstraints": []}, "mfa_amr_claims": {"id": "auth.mfa_amr_claims", "schemaName": "auth", "tableName": "mfa_amr_claims", "fields": [{"id": "auth.mfa_amr_claims.session_id", "name": "session_id", "columnName": "session_id", "type": "uuid", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.mfa_amr_claims.created_at", "name": "created_at", "columnName": "created_at", "type": "timestamptz", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.mfa_amr_claims.updated_at", "name": "updated_at", "columnName": "updated_at", "type": "timestamptz", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.mfa_amr_claims.authentication_method", "name": "authentication_method", "columnName": "authentication_method", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.mfa_amr_claims.id", "name": "id", "columnName": "id", "type": "uuid", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": true, "maxLength": null}, {"name": "sessions", "type": "sessions", "isRequired": true, "kind": "object", "relationName": "mfa_amr_claimsTosessions", "relationFromFields": ["session_id"], "relationToFields": ["id"], "isList": false, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}], "uniqueConstraints": [{"name": "mfa_amr_claims_session_id_authentication_method_pkey", "fields": ["authentication_method", "session_id"], "nullNotDistinct": false}]}, "mfa_challenges": {"id": "auth.mfa_challenges", "schemaName": "auth", "tableName": "mfa_challenges", "fields": [{"id": "auth.mfa_challenges.id", "name": "id", "columnName": "id", "type": "uuid", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": true, "maxLength": null}, {"id": "auth.mfa_challenges.factor_id", "name": "factor_id", "columnName": "factor_id", "type": "uuid", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.mfa_challenges.created_at", "name": "created_at", "columnName": "created_at", "type": "timestamptz", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.mfa_challenges.verified_at", "name": "verified_at", "columnName": "verified_at", "type": "timestamptz", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.mfa_challenges.ip_address", "name": "ip_address", "columnName": "ip_address", "type": "inet", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.mfa_challenges.otp_code", "name": "otp_code", "columnName": "otp_code", "type": "text", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.mfa_challenges.web_authn_session_data", "name": "web_authn_session_data", "columnName": "web_authn_session_data", "type": "jsonb", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"name": "mfa_factors", "type": "mfa_factors", "isRequired": true, "kind": "object", "relationName": "mfa_challengesTomfa_factors", "relationFromFields": ["factor_id"], "relationToFields": ["id"], "isList": false, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}], "uniqueConstraints": []}, "mfa_factors": {"id": "auth.mfa_factors", "schemaName": "auth", "tableName": "mfa_factors", "fields": [{"id": "auth.mfa_factors.id", "name": "id", "columnName": "id", "type": "uuid", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": true, "maxLength": null}, {"id": "auth.mfa_factors.user_id", "name": "user_id", "columnName": "user_id", "type": "uuid", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.mfa_factors.friendly_name", "name": "friendly_name", "columnName": "friendly_name", "type": "text", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.mfa_factors.factor_type", "name": "factor_type", "columnName": "factor_type", "type": "factor_type", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.mfa_factors.status", "name": "status", "columnName": "status", "type": "factor_status", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.mfa_factors.created_at", "name": "created_at", "columnName": "created_at", "type": "timestamptz", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.mfa_factors.updated_at", "name": "updated_at", "columnName": "updated_at", "type": "timestamptz", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.mfa_factors.secret", "name": "secret", "columnName": "secret", "type": "text", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.mfa_factors.phone", "name": "phone", "columnName": "phone", "type": "text", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.mfa_factors.last_challenged_at", "name": "last_challenged_at", "columnName": "last_challenged_at", "type": "timestamptz", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.mfa_factors.web_authn_credential", "name": "web_authn_credential", "columnName": "web_authn_credential", "type": "jsonb", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.mfa_factors.web_authn_aaguid", "name": "web_authn_aaguid", "columnName": "web_authn_aaguid", "type": "uuid", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"name": "users", "type": "users", "isRequired": true, "kind": "object", "relationName": "mfa_factorsTousers", "relationFromFields": ["user_id"], "relationToFields": ["id"], "isList": false, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}, {"name": "mfa_challenges", "type": "mfa_challenges", "isRequired": false, "kind": "object", "relationName": "mfa_challengesTomfa_factors", "relationFromFields": [], "relationToFields": [], "isList": true, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}], "uniqueConstraints": [{"name": "mfa_factors_last_challenged_at_key", "fields": ["last_challenged_at"], "nullNotDistinct": false}, {"name": "mfa_factors_user_friendly_name_unique", "fields": ["friendly_name", "user_id"], "nullNotDistinct": false}, {"name": "unique_phone_factor_per_user", "fields": ["phone", "user_id"], "nullNotDistinct": false}]}, "storage_migrations": {"id": "storage.migrations", "schemaName": "storage", "tableName": "migrations", "fields": [{"id": "storage.migrations.id", "name": "id", "columnName": "id", "type": "int4", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": true, "maxLength": null}, {"id": "storage.migrations.name", "name": "name", "columnName": "name", "type": "<PERSON><PERSON><PERSON>", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": 100}, {"id": "storage.migrations.hash", "name": "hash", "columnName": "hash", "type": "<PERSON><PERSON><PERSON>", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": 40}, {"id": "storage.migrations.executed_at", "name": "executed_at", "columnName": "executed_at", "type": "timestamp", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false, "maxLength": null}], "uniqueConstraints": [{"name": "migrations_name_key", "fields": ["name"], "nullNotDistinct": false}]}, "supabase_functions_migrations": {"id": "supabase_functions.migrations", "schemaName": "supabase_functions", "tableName": "migrations", "fields": [{"id": "supabase_functions.migrations.version", "name": "version", "columnName": "version", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": true, "maxLength": null}, {"id": "supabase_functions.migrations.inserted_at", "name": "inserted_at", "columnName": "inserted_at", "type": "timestamptz", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false, "maxLength": null}], "uniqueConstraints": [{"name": "migrations_pkey", "fields": ["version"], "nullNotDistinct": false}]}, "objects": {"id": "storage.objects", "schemaName": "storage", "tableName": "objects", "fields": [{"id": "storage.objects.id", "name": "id", "columnName": "id", "type": "uuid", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": true, "maxLength": null}, {"id": "storage.objects.bucket_id", "name": "bucket_id", "columnName": "bucket_id", "type": "text", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "storage.objects.name", "name": "name", "columnName": "name", "type": "text", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "storage.objects.owner", "name": "owner", "columnName": "owner", "type": "uuid", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "storage.objects.created_at", "name": "created_at", "columnName": "created_at", "type": "timestamptz", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false, "maxLength": null}, {"id": "storage.objects.updated_at", "name": "updated_at", "columnName": "updated_at", "type": "timestamptz", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false, "maxLength": null}, {"id": "storage.objects.last_accessed_at", "name": "last_accessed_at", "columnName": "last_accessed_at", "type": "timestamptz", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false, "maxLength": null}, {"id": "storage.objects.metadata", "name": "metadata", "columnName": "metadata", "type": "jsonb", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "storage.objects.path_tokens", "name": "path_tokens", "columnName": "path_tokens", "type": "text[]", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": true, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "storage.objects.version", "name": "version", "columnName": "version", "type": "text", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "storage.objects.owner_id", "name": "owner_id", "columnName": "owner_id", "type": "text", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "storage.objects.user_metadata", "name": "user_metadata", "columnName": "user_metadata", "type": "jsonb", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "storage.objects.level", "name": "level", "columnName": "level", "type": "int4", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"name": "buckets", "type": "buckets", "isRequired": false, "kind": "object", "relationName": "objectsTobuckets", "relationFromFields": ["bucket_id"], "relationToFields": ["id"], "isList": false, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}], "uniqueConstraints": [{"name": "bucketid_objname", "fields": ["bucket_id", "name"], "nullNotDistinct": false}, {"name": "idx_name_bucket_level_unique", "fields": ["bucket_id", "level", "name"], "nullNotDistinct": false}, {"name": "objects_bucket_id_level_idx", "fields": ["bucket_id", "level", "name"], "nullNotDistinct": false}]}, "one_time_tokens": {"id": "auth.one_time_tokens", "schemaName": "auth", "tableName": "one_time_tokens", "fields": [{"id": "auth.one_time_tokens.id", "name": "id", "columnName": "id", "type": "uuid", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": true, "maxLength": null}, {"id": "auth.one_time_tokens.user_id", "name": "user_id", "columnName": "user_id", "type": "uuid", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.one_time_tokens.token_type", "name": "token_type", "columnName": "token_type", "type": "one_time_token_type", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.one_time_tokens.token_hash", "name": "token_hash", "columnName": "token_hash", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.one_time_tokens.relates_to", "name": "relates_to", "columnName": "relates_to", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.one_time_tokens.created_at", "name": "created_at", "columnName": "created_at", "type": "timestamp", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false, "maxLength": null}, {"id": "auth.one_time_tokens.updated_at", "name": "updated_at", "columnName": "updated_at", "type": "timestamp", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false, "maxLength": null}, {"name": "users", "type": "users", "isRequired": true, "kind": "object", "relationName": "one_time_tokensTousers", "relationFromFields": ["user_id"], "relationToFields": ["id"], "isList": false, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}], "uniqueConstraints": [{"name": "one_time_tokens_user_id_token_type_key", "fields": ["token_type", "user_id"], "nullNotDistinct": false}]}, "organization": {"id": "public.organization", "schemaName": "public", "tableName": "organization", "fields": [{"id": "public.organization.id", "name": "id", "columnName": "id", "type": "uuid", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": true, "maxLength": null}, {"id": "public.organization.consultancy_organization_id", "name": "consultancy_organization_id", "columnName": "consultancy_organization_id", "type": "uuid", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "public.organization.name", "name": "name", "columnName": "name", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "public.organization.type", "name": "type", "columnName": "type", "type": "organization_type", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "public.organization.line1", "name": "line1", "columnName": "line1", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "public.organization.line2", "name": "line2", "columnName": "line2", "type": "text", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "public.organization.postal_code", "name": "postal_code", "columnName": "postal_code", "type": "text", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "public.organization.city", "name": "city", "columnName": "city", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "public.organization.state", "name": "state", "columnName": "state", "type": "text", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "public.organization.country", "name": "country", "columnName": "country", "type": "bpchar", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": 2}, {"id": "public.organization.created_at", "name": "created_at", "columnName": "created_at", "type": "timestamptz", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false, "maxLength": null}, {"id": "public.organization.created_by", "name": "created_by", "columnName": "created_by", "type": "uuid", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "public.organization.updated_at", "name": "updated_at", "columnName": "updated_at", "type": "timestamptz", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false, "maxLength": null}, {"id": "public.organization.updated_by", "name": "updated_by", "columnName": "updated_by", "type": "uuid", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"name": "users_organization_created_byTousers", "type": "users", "isRequired": true, "kind": "object", "relationName": "organization_created_byTousers", "relationFromFields": ["created_by"], "relationToFields": ["id"], "isList": false, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}, {"name": "users_organization_updated_byTousers", "type": "users", "isRequired": true, "kind": "object", "relationName": "organization_updated_byTousers", "relationFromFields": ["updated_by"], "relationToFields": ["id"], "isList": false, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}, {"name": "country", "type": "country", "isRequired": true, "kind": "object", "relationName": "organizationTocountry", "relationFromFields": ["country"], "relationToFields": ["code"], "isList": false, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}, {"name": "organization", "type": "organization", "isRequired": false, "kind": "object", "relationName": "organizationToorganization", "relationFromFields": ["consultancy_organization_id"], "relationToFields": ["id"], "isList": false, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}, {"name": "organization", "type": "organization", "isRequired": false, "kind": "object", "relationName": "organizationToorganization", "relationFromFields": [], "relationToFields": [], "isList": true, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}, {"name": "profile", "type": "profile", "isRequired": false, "kind": "object", "relationName": "profileToorganization", "relationFromFields": [], "relationToFields": [], "isList": true, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}], "uniqueConstraints": [{"name": "organization_pkey", "fields": ["id"], "nullNotDistinct": false}]}, "permission": {"id": "public.permission", "schemaName": "public", "tableName": "permission", "fields": [{"id": "public.permission.name", "name": "name", "columnName": "name", "type": "<PERSON><PERSON><PERSON>", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": true, "maxLength": 50}, {"name": "role_permission", "type": "role_permission", "isRequired": false, "kind": "object", "relationName": "role_permissionTopermission", "relationFromFields": [], "relationToFields": [], "isList": true, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}], "uniqueConstraints": [{"name": "permission_pkey", "fields": ["name"], "nullNotDistinct": false}]}, "prefixes": {"id": "storage.prefixes", "schemaName": "storage", "tableName": "prefixes", "fields": [{"id": "storage.prefixes.bucket_id", "name": "bucket_id", "columnName": "bucket_id", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": true, "maxLength": null}, {"id": "storage.prefixes.name", "name": "name", "columnName": "name", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": true, "maxLength": null}, {"id": "storage.prefixes.level", "name": "level", "columnName": "level", "type": "int4", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": true, "sequence": false, "hasDefaultValue": false, "isId": true, "maxLength": null}, {"id": "storage.prefixes.created_at", "name": "created_at", "columnName": "created_at", "type": "timestamptz", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false, "maxLength": null}, {"id": "storage.prefixes.updated_at", "name": "updated_at", "columnName": "updated_at", "type": "timestamptz", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false, "maxLength": null}, {"name": "buckets", "type": "buckets", "isRequired": true, "kind": "object", "relationName": "prefixesTobuckets", "relationFromFields": ["bucket_id"], "relationToFields": ["id"], "isList": false, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}], "uniqueConstraints": []}, "profile": {"id": "public.profile", "schemaName": "public", "tableName": "profile", "fields": [{"id": "public.profile.id", "name": "id", "columnName": "id", "type": "uuid", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": true, "maxLength": null}, {"id": "public.profile.first_name", "name": "first_name", "columnName": "first_name", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "public.profile.last_name", "name": "last_name", "columnName": "last_name", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "public.profile.gender", "name": "gender", "columnName": "gender", "type": "gender", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "public.profile.phone_number", "name": "phone_number", "columnName": "phone_number", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "public.profile.organization_id", "name": "organization_id", "columnName": "organization_id", "type": "uuid", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "public.profile.role", "name": "role", "columnName": "role", "type": "role", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "public.profile.created_at", "name": "created_at", "columnName": "created_at", "type": "timestamptz", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false, "maxLength": null}, {"id": "public.profile.created_by", "name": "created_by", "columnName": "created_by", "type": "uuid", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "public.profile.updated_at", "name": "updated_at", "columnName": "updated_at", "type": "timestamptz", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false, "maxLength": null}, {"id": "public.profile.updated_by", "name": "updated_by", "columnName": "updated_by", "type": "uuid", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"name": "users_profile_created_byTousers", "type": "users", "isRequired": true, "kind": "object", "relationName": "profile_created_byTousers", "relationFromFields": ["created_by"], "relationToFields": ["id"], "isList": false, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}, {"name": "users_profile_idTousers", "type": "users", "isRequired": true, "kind": "object", "relationName": "profile_idTousers", "relationFromFields": ["id"], "relationToFields": ["id"], "isList": false, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}, {"name": "users_profile_updated_byTousers", "type": "users", "isRequired": true, "kind": "object", "relationName": "profile_updated_byTousers", "relationFromFields": ["updated_by"], "relationToFields": ["id"], "isList": false, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}, {"name": "organization", "type": "organization", "isRequired": true, "kind": "object", "relationName": "profileToorganization", "relationFromFields": ["organization_id"], "relationToFields": ["id"], "isList": false, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}], "uniqueConstraints": [{"name": "profile_pkey", "fields": ["id"], "nullNotDistinct": false}]}, "refresh_tokens": {"id": "auth.refresh_tokens", "schemaName": "auth", "tableName": "refresh_tokens", "fields": [{"id": "auth.refresh_tokens.instance_id", "name": "instance_id", "columnName": "instance_id", "type": "uuid", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.refresh_tokens.id", "name": "id", "columnName": "id", "type": "int8", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": {"identifier": "\"auth\".\"refresh_tokens_id_seq\"", "increment": 1, "start": 1}, "hasDefaultValue": true, "isId": true, "maxLength": null}, {"id": "auth.refresh_tokens.token", "name": "token", "columnName": "token", "type": "<PERSON><PERSON><PERSON>", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": 255}, {"id": "auth.refresh_tokens.user_id", "name": "user_id", "columnName": "user_id", "type": "<PERSON><PERSON><PERSON>", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": 255}, {"id": "auth.refresh_tokens.revoked", "name": "revoked", "columnName": "revoked", "type": "bool", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.refresh_tokens.created_at", "name": "created_at", "columnName": "created_at", "type": "timestamptz", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.refresh_tokens.updated_at", "name": "updated_at", "columnName": "updated_at", "type": "timestamptz", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.refresh_tokens.parent", "name": "parent", "columnName": "parent", "type": "<PERSON><PERSON><PERSON>", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": 255}, {"id": "auth.refresh_tokens.session_id", "name": "session_id", "columnName": "session_id", "type": "uuid", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"name": "sessions", "type": "sessions", "isRequired": false, "kind": "object", "relationName": "refresh_tokensTosessions", "relationFromFields": ["session_id"], "relationToFields": ["id"], "isList": false, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}], "uniqueConstraints": [{"name": "refresh_tokens_token_unique", "fields": ["token"], "nullNotDistinct": false}]}, "role_permission": {"id": "public.role_permission", "schemaName": "public", "tableName": "role_permission", "fields": [{"id": "public.role_permission.role", "name": "role", "columnName": "role", "type": "role", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": true, "maxLength": null}, {"id": "public.role_permission.permission", "name": "permission", "columnName": "permission", "type": "<PERSON><PERSON><PERSON>", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": true, "maxLength": 50}, {"name": "permission", "type": "permission", "isRequired": true, "kind": "object", "relationName": "role_permissionTopermission", "relationFromFields": ["permission"], "relationToFields": ["name"], "isList": false, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}], "uniqueConstraints": [{"name": "role_permission_pkey", "fields": ["permission", "role"], "nullNotDistinct": false}]}, "s3_multipart_uploads": {"id": "storage.s3_multipart_uploads", "schemaName": "storage", "tableName": "s3_multipart_uploads", "fields": [{"id": "storage.s3_multipart_uploads.id", "name": "id", "columnName": "id", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": true, "maxLength": null}, {"id": "storage.s3_multipart_uploads.in_progress_size", "name": "in_progress_size", "columnName": "in_progress_size", "type": "int8", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false, "maxLength": null}, {"id": "storage.s3_multipart_uploads.upload_signature", "name": "upload_signature", "columnName": "upload_signature", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "storage.s3_multipart_uploads.bucket_id", "name": "bucket_id", "columnName": "bucket_id", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "storage.s3_multipart_uploads.key", "name": "key", "columnName": "key", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "storage.s3_multipart_uploads.version", "name": "version", "columnName": "version", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "storage.s3_multipart_uploads.owner_id", "name": "owner_id", "columnName": "owner_id", "type": "text", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "storage.s3_multipart_uploads.created_at", "name": "created_at", "columnName": "created_at", "type": "timestamptz", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false, "maxLength": null}, {"id": "storage.s3_multipart_uploads.user_metadata", "name": "user_metadata", "columnName": "user_metadata", "type": "jsonb", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"name": "buckets", "type": "buckets", "isRequired": true, "kind": "object", "relationName": "s3_multipart_uploadsTobuckets", "relationFromFields": ["bucket_id"], "relationToFields": ["id"], "isList": false, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}, {"name": "s3_multipart_uploads_parts", "type": "s3_multipart_uploads_parts", "isRequired": false, "kind": "object", "relationName": "s3_multipart_uploads_partsTos3_multipart_uploads", "relationFromFields": [], "relationToFields": [], "isList": true, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}], "uniqueConstraints": []}, "s3_multipart_uploads_parts": {"id": "storage.s3_multipart_uploads_parts", "schemaName": "storage", "tableName": "s3_multipart_uploads_parts", "fields": [{"id": "storage.s3_multipart_uploads_parts.id", "name": "id", "columnName": "id", "type": "uuid", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": true, "maxLength": null}, {"id": "storage.s3_multipart_uploads_parts.upload_id", "name": "upload_id", "columnName": "upload_id", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "storage.s3_multipart_uploads_parts.size", "name": "size", "columnName": "size", "type": "int8", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false, "maxLength": null}, {"id": "storage.s3_multipart_uploads_parts.part_number", "name": "part_number", "columnName": "part_number", "type": "int4", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "storage.s3_multipart_uploads_parts.bucket_id", "name": "bucket_id", "columnName": "bucket_id", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "storage.s3_multipart_uploads_parts.key", "name": "key", "columnName": "key", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "storage.s3_multipart_uploads_parts.etag", "name": "etag", "columnName": "etag", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "storage.s3_multipart_uploads_parts.owner_id", "name": "owner_id", "columnName": "owner_id", "type": "text", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "storage.s3_multipart_uploads_parts.version", "name": "version", "columnName": "version", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "storage.s3_multipart_uploads_parts.created_at", "name": "created_at", "columnName": "created_at", "type": "timestamptz", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false, "maxLength": null}, {"name": "buckets", "type": "buckets", "isRequired": true, "kind": "object", "relationName": "s3_multipart_uploads_partsTobuckets", "relationFromFields": ["bucket_id"], "relationToFields": ["id"], "isList": false, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}, {"name": "s3_multipart_uploads", "type": "s3_multipart_uploads", "isRequired": true, "kind": "object", "relationName": "s3_multipart_uploads_partsTos3_multipart_uploads", "relationFromFields": ["upload_id"], "relationToFields": ["id"], "isList": false, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}], "uniqueConstraints": []}, "saml_providers": {"id": "auth.saml_providers", "schemaName": "auth", "tableName": "saml_providers", "fields": [{"id": "auth.saml_providers.id", "name": "id", "columnName": "id", "type": "uuid", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": true, "maxLength": null}, {"id": "auth.saml_providers.sso_provider_id", "name": "sso_provider_id", "columnName": "sso_provider_id", "type": "uuid", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.saml_providers.entity_id", "name": "entity_id", "columnName": "entity_id", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.saml_providers.metadata_xml", "name": "metadata_xml", "columnName": "metadata_xml", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.saml_providers.metadata_url", "name": "metadata_url", "columnName": "metadata_url", "type": "text", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.saml_providers.attribute_mapping", "name": "attribute_mapping", "columnName": "attribute_mapping", "type": "jsonb", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.saml_providers.created_at", "name": "created_at", "columnName": "created_at", "type": "timestamptz", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.saml_providers.updated_at", "name": "updated_at", "columnName": "updated_at", "type": "timestamptz", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.saml_providers.name_id_format", "name": "name_id_format", "columnName": "name_id_format", "type": "text", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"name": "sso_providers", "type": "sso_providers", "isRequired": true, "kind": "object", "relationName": "saml_providersTosso_providers", "relationFromFields": ["sso_provider_id"], "relationToFields": ["id"], "isList": false, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}], "uniqueConstraints": [{"name": "saml_providers_entity_id_key", "fields": ["entity_id"], "nullNotDistinct": false}]}, "saml_relay_states": {"id": "auth.saml_relay_states", "schemaName": "auth", "tableName": "saml_relay_states", "fields": [{"id": "auth.saml_relay_states.id", "name": "id", "columnName": "id", "type": "uuid", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": true, "maxLength": null}, {"id": "auth.saml_relay_states.sso_provider_id", "name": "sso_provider_id", "columnName": "sso_provider_id", "type": "uuid", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.saml_relay_states.request_id", "name": "request_id", "columnName": "request_id", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.saml_relay_states.for_email", "name": "for_email", "columnName": "for_email", "type": "text", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.saml_relay_states.redirect_to", "name": "redirect_to", "columnName": "redirect_to", "type": "text", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.saml_relay_states.created_at", "name": "created_at", "columnName": "created_at", "type": "timestamptz", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.saml_relay_states.updated_at", "name": "updated_at", "columnName": "updated_at", "type": "timestamptz", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.saml_relay_states.flow_state_id", "name": "flow_state_id", "columnName": "flow_state_id", "type": "uuid", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"name": "flow_state", "type": "flow_state", "isRequired": false, "kind": "object", "relationName": "saml_relay_statesToflow_state", "relationFromFields": ["flow_state_id"], "relationToFields": ["id"], "isList": false, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}, {"name": "sso_providers", "type": "sso_providers", "isRequired": true, "kind": "object", "relationName": "saml_relay_statesTosso_providers", "relationFromFields": ["sso_provider_id"], "relationToFields": ["id"], "isList": false, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}], "uniqueConstraints": []}, "auth_schema_migrations": {"id": "auth.schema_migrations", "schemaName": "auth", "tableName": "schema_migrations", "fields": [{"id": "auth.schema_migrations.version", "name": "version", "columnName": "version", "type": "<PERSON><PERSON><PERSON>", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": true, "maxLength": 255}], "uniqueConstraints": []}, "supabase_migrations_schema_migrations": {"id": "supabase_migrations.schema_migrations", "schemaName": "supabase_migrations", "tableName": "schema_migrations", "fields": [{"id": "supabase_migrations.schema_migrations.version", "name": "version", "columnName": "version", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": true, "maxLength": null}, {"id": "supabase_migrations.schema_migrations.statements", "name": "statements", "columnName": "statements", "type": "text[]", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "supabase_migrations.schema_migrations.name", "name": "name", "columnName": "name", "type": "text", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}], "uniqueConstraints": [{"name": "schema_migrations_pkey", "fields": ["version"], "nullNotDistinct": false}]}, "secrets": {"id": "vault.secrets", "schemaName": "vault", "tableName": "secrets", "fields": [{"id": "vault.secrets.id", "name": "id", "columnName": "id", "type": "uuid", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": true, "maxLength": null}, {"id": "vault.secrets.name", "name": "name", "columnName": "name", "type": "text", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "vault.secrets.description", "name": "description", "columnName": "description", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false, "maxLength": null}, {"id": "vault.secrets.secret", "name": "secret", "columnName": "secret", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "vault.secrets.key_id", "name": "key_id", "columnName": "key_id", "type": "uuid", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "vault.secrets.nonce", "name": "nonce", "columnName": "nonce", "type": "bytea", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false, "maxLength": null}, {"id": "vault.secrets.created_at", "name": "created_at", "columnName": "created_at", "type": "timestamptz", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false, "maxLength": null}, {"id": "vault.secrets.updated_at", "name": "updated_at", "columnName": "updated_at", "type": "timestamptz", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false, "maxLength": null}], "uniqueConstraints": [{"name": "secrets_name_idx", "fields": ["name"], "nullNotDistinct": false}]}, "seed_files": {"id": "supabase_migrations.seed_files", "schemaName": "supabase_migrations", "tableName": "seed_files", "fields": [{"id": "supabase_migrations.seed_files.path", "name": "path", "columnName": "path", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": true, "maxLength": null}, {"id": "supabase_migrations.seed_files.hash", "name": "hash", "columnName": "hash", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}], "uniqueConstraints": [{"name": "seed_files_pkey", "fields": ["path"], "nullNotDistinct": false}]}, "sessions": {"id": "auth.sessions", "schemaName": "auth", "tableName": "sessions", "fields": [{"id": "auth.sessions.id", "name": "id", "columnName": "id", "type": "uuid", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": true, "maxLength": null}, {"id": "auth.sessions.user_id", "name": "user_id", "columnName": "user_id", "type": "uuid", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.sessions.created_at", "name": "created_at", "columnName": "created_at", "type": "timestamptz", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.sessions.updated_at", "name": "updated_at", "columnName": "updated_at", "type": "timestamptz", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.sessions.factor_id", "name": "factor_id", "columnName": "factor_id", "type": "uuid", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.sessions.aal", "name": "aal", "columnName": "aal", "type": "aal_level", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.sessions.not_after", "name": "not_after", "columnName": "not_after", "type": "timestamptz", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.sessions.refreshed_at", "name": "refreshed_at", "columnName": "refreshed_at", "type": "timestamp", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.sessions.user_agent", "name": "user_agent", "columnName": "user_agent", "type": "text", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.sessions.ip", "name": "ip", "columnName": "ip", "type": "inet", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.sessions.tag", "name": "tag", "columnName": "tag", "type": "text", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"name": "users", "type": "users", "isRequired": true, "kind": "object", "relationName": "sessionsTousers", "relationFromFields": ["user_id"], "relationToFields": ["id"], "isList": false, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}, {"name": "mfa_amr_claims", "type": "mfa_amr_claims", "isRequired": false, "kind": "object", "relationName": "mfa_amr_claimsTosessions", "relationFromFields": [], "relationToFields": [], "isList": true, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}, {"name": "refresh_tokens", "type": "refresh_tokens", "isRequired": false, "kind": "object", "relationName": "refresh_tokensTosessions", "relationFromFields": [], "relationToFields": [], "isList": true, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}], "uniqueConstraints": []}, "sso_domains": {"id": "auth.sso_domains", "schemaName": "auth", "tableName": "sso_domains", "fields": [{"id": "auth.sso_domains.id", "name": "id", "columnName": "id", "type": "uuid", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": true, "maxLength": null}, {"id": "auth.sso_domains.sso_provider_id", "name": "sso_provider_id", "columnName": "sso_provider_id", "type": "uuid", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.sso_domains.domain", "name": "domain", "columnName": "domain", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.sso_domains.created_at", "name": "created_at", "columnName": "created_at", "type": "timestamptz", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.sso_domains.updated_at", "name": "updated_at", "columnName": "updated_at", "type": "timestamptz", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"name": "sso_providers", "type": "sso_providers", "isRequired": true, "kind": "object", "relationName": "sso_domainsTosso_providers", "relationFromFields": ["sso_provider_id"], "relationToFields": ["id"], "isList": false, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}], "uniqueConstraints": []}, "sso_providers": {"id": "auth.sso_providers", "schemaName": "auth", "tableName": "sso_providers", "fields": [{"id": "auth.sso_providers.id", "name": "id", "columnName": "id", "type": "uuid", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": true, "maxLength": null}, {"id": "auth.sso_providers.resource_id", "name": "resource_id", "columnName": "resource_id", "type": "text", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.sso_providers.created_at", "name": "created_at", "columnName": "created_at", "type": "timestamptz", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.sso_providers.updated_at", "name": "updated_at", "columnName": "updated_at", "type": "timestamptz", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"name": "saml_providers", "type": "saml_providers", "isRequired": false, "kind": "object", "relationName": "saml_providersTosso_providers", "relationFromFields": [], "relationToFields": [], "isList": true, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}, {"name": "saml_relay_states", "type": "saml_relay_states", "isRequired": false, "kind": "object", "relationName": "saml_relay_statesTosso_providers", "relationFromFields": [], "relationToFields": [], "isList": true, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}, {"name": "sso_domains", "type": "sso_domains", "isRequired": false, "kind": "object", "relationName": "sso_domainsTosso_providers", "relationFromFields": [], "relationToFields": [], "isList": true, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}], "uniqueConstraints": []}, "users": {"id": "auth.users", "schemaName": "auth", "tableName": "users", "fields": [{"id": "auth.users.instance_id", "name": "instance_id", "columnName": "instance_id", "type": "uuid", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.users.id", "name": "id", "columnName": "id", "type": "uuid", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": true, "maxLength": null}, {"id": "auth.users.aud", "name": "aud", "columnName": "aud", "type": "<PERSON><PERSON><PERSON>", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": 255}, {"id": "auth.users.role", "name": "role", "columnName": "role", "type": "<PERSON><PERSON><PERSON>", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": 255}, {"id": "auth.users.email", "name": "email", "columnName": "email", "type": "<PERSON><PERSON><PERSON>", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": 255}, {"id": "auth.users.encrypted_password", "name": "encrypted_password", "columnName": "encrypted_password", "type": "<PERSON><PERSON><PERSON>", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": 255}, {"id": "auth.users.email_confirmed_at", "name": "email_confirmed_at", "columnName": "email_confirmed_at", "type": "timestamptz", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.users.invited_at", "name": "invited_at", "columnName": "invited_at", "type": "timestamptz", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.users.confirmation_token", "name": "confirmation_token", "columnName": "confirmation_token", "type": "<PERSON><PERSON><PERSON>", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": 255}, {"id": "auth.users.confirmation_sent_at", "name": "confirmation_sent_at", "columnName": "confirmation_sent_at", "type": "timestamptz", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.users.recovery_token", "name": "recovery_token", "columnName": "recovery_token", "type": "<PERSON><PERSON><PERSON>", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": 255}, {"id": "auth.users.recovery_sent_at", "name": "recovery_sent_at", "columnName": "recovery_sent_at", "type": "timestamptz", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.users.email_change_token_new", "name": "email_change_token_new", "columnName": "email_change_token_new", "type": "<PERSON><PERSON><PERSON>", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": 255}, {"id": "auth.users.email_change", "name": "email_change", "columnName": "email_change", "type": "<PERSON><PERSON><PERSON>", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": 255}, {"id": "auth.users.email_change_sent_at", "name": "email_change_sent_at", "columnName": "email_change_sent_at", "type": "timestamptz", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.users.last_sign_in_at", "name": "last_sign_in_at", "columnName": "last_sign_in_at", "type": "timestamptz", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.users.raw_app_meta_data", "name": "raw_app_meta_data", "columnName": "raw_app_meta_data", "type": "jsonb", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.users.raw_user_meta_data", "name": "raw_user_meta_data", "columnName": "raw_user_meta_data", "type": "jsonb", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.users.is_super_admin", "name": "is_super_admin", "columnName": "is_super_admin", "type": "bool", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.users.created_at", "name": "created_at", "columnName": "created_at", "type": "timestamptz", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.users.updated_at", "name": "updated_at", "columnName": "updated_at", "type": "timestamptz", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.users.phone", "name": "phone", "columnName": "phone", "type": "text", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false, "maxLength": null}, {"id": "auth.users.phone_confirmed_at", "name": "phone_confirmed_at", "columnName": "phone_confirmed_at", "type": "timestamptz", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.users.phone_change", "name": "phone_change", "columnName": "phone_change", "type": "text", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false, "maxLength": null}, {"id": "auth.users.phone_change_token", "name": "phone_change_token", "columnName": "phone_change_token", "type": "<PERSON><PERSON><PERSON>", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false, "maxLength": 255}, {"id": "auth.users.phone_change_sent_at", "name": "phone_change_sent_at", "columnName": "phone_change_sent_at", "type": "timestamptz", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.users.confirmed_at", "name": "confirmed_at", "columnName": "confirmed_at", "type": "timestamptz", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": true, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.users.email_change_token_current", "name": "email_change_token_current", "columnName": "email_change_token_current", "type": "<PERSON><PERSON><PERSON>", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false, "maxLength": 255}, {"id": "auth.users.email_change_confirm_status", "name": "email_change_confirm_status", "columnName": "email_change_confirm_status", "type": "int2", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false, "maxLength": null}, {"id": "auth.users.banned_until", "name": "banned_until", "columnName": "banned_until", "type": "timestamptz", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.users.reauthentication_token", "name": "reauthentication_token", "columnName": "reauthentication_token", "type": "<PERSON><PERSON><PERSON>", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false, "maxLength": 255}, {"id": "auth.users.reauthentication_sent_at", "name": "reauthentication_sent_at", "columnName": "reauthentication_sent_at", "type": "timestamptz", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.users.is_sso_user", "name": "is_sso_user", "columnName": "is_sso_user", "type": "bool", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false, "maxLength": null}, {"id": "auth.users.deleted_at", "name": "deleted_at", "columnName": "deleted_at", "type": "timestamptz", "isRequired": false, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false, "maxLength": null}, {"id": "auth.users.is_anonymous", "name": "is_anonymous", "columnName": "is_anonymous", "type": "bool", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false, "maxLength": null}, {"name": "identities", "type": "identities", "isRequired": false, "kind": "object", "relationName": "identitiesTousers", "relationFromFields": [], "relationToFields": [], "isList": true, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}, {"name": "mfa_factors", "type": "mfa_factors", "isRequired": false, "kind": "object", "relationName": "mfa_factorsTousers", "relationFromFields": [], "relationToFields": [], "isList": true, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}, {"name": "one_time_tokens", "type": "one_time_tokens", "isRequired": false, "kind": "object", "relationName": "one_time_tokensTousers", "relationFromFields": [], "relationToFields": [], "isList": true, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}, {"name": "sessions", "type": "sessions", "isRequired": false, "kind": "object", "relationName": "sessionsTousers", "relationFromFields": [], "relationToFields": [], "isList": true, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}, {"name": "organization_organization_created_byTousers", "type": "organization", "isRequired": false, "kind": "object", "relationName": "organization_created_byTousers", "relationFromFields": [], "relationToFields": [], "isList": true, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}, {"name": "organization_organization_updated_byTousers", "type": "organization", "isRequired": false, "kind": "object", "relationName": "organization_updated_byTousers", "relationFromFields": [], "relationToFields": [], "isList": true, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}, {"name": "profile_profile_created_byTousers", "type": "profile", "isRequired": false, "kind": "object", "relationName": "profile_created_byTousers", "relationFromFields": [], "relationToFields": [], "isList": true, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}, {"name": "profile_profile_idTousers", "type": "profile", "isRequired": false, "kind": "object", "relationName": "profile_idTousers", "relationFromFields": [], "relationToFields": [], "isList": true, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}, {"name": "profile_profile_updated_byTousers", "type": "profile", "isRequired": false, "kind": "object", "relationName": "profile_updated_byTousers", "relationFromFields": [], "relationToFields": [], "isList": true, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}], "uniqueConstraints": [{"name": "confirmation_token_idx", "fields": ["confirmation_token"], "nullNotDistinct": false}, {"name": "email_change_token_current_idx", "fields": ["email_change_token_current"], "nullNotDistinct": false}, {"name": "email_change_token_new_idx", "fields": ["email_change_token_new"], "nullNotDistinct": false}, {"name": "reauthentication_token_idx", "fields": ["reauthentication_token"], "nullNotDistinct": false}, {"name": "recovery_token_idx", "fields": ["recovery_token"], "nullNotDistinct": false}, {"name": "users_email_partial_key", "fields": ["email"], "nullNotDistinct": false}, {"name": "users_phone_key", "fields": ["phone"], "nullNotDistinct": false}]}}, "enums": {"aal_level": {"schemaName": "auth", "values": [{"name": "aal1"}, {"name": "aal2"}, {"name": "aal3"}]}, "code_challenge_method": {"schemaName": "auth", "values": [{"name": "plain"}, {"name": "s256"}]}, "factor_status": {"schemaName": "auth", "values": [{"name": "unverified"}, {"name": "verified"}]}, "factor_type": {"schemaName": "auth", "values": [{"name": "phone"}, {"name": "totp"}, {"name": "webauthn"}]}, "one_time_token_type": {"schemaName": "auth", "values": [{"name": "confirmation_token"}, {"name": "email_change_token_current"}, {"name": "email_change_token_new"}, {"name": "phone_change_token"}, {"name": "reauthentication_token"}, {"name": "recovery_token"}]}, "request_status": {"schemaName": "net", "values": [{"name": "ERROR"}, {"name": "PENDING"}, {"name": "SUCCESS"}]}, "gender": {"schemaName": "public", "values": [{"name": "female"}, {"name": "male"}, {"name": "other"}]}, "organization_type": {"schemaName": "public", "values": [{"name": "consultancy"}, {"name": "tenant"}]}, "role": {"schemaName": "public", "values": [{"name": "consultancy_employee"}, {"name": "consultancy_manager"}, {"name": "tenant_admin"}, {"name": "tenant_compliance"}, {"name": "tenant_purchaser"}, {"name": "tenant_seller"}]}}}