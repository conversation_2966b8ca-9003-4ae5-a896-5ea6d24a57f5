<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IBM Plex Font Demo | Refined UI Edition</title>

    <!-- Google Fonts: IBM Plex Serif and IBM Plex Sans -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;1,400&family=IBM+Plex+Serif:ital,wght@0,300;0,400;0,500;0,600;1,400&display=swap"
          rel="stylesheet">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Custom CSS & Tailwind Config -->
    <style>
        /* --- Refined UI Edition Styles --- */
        body {
            font-family: 'IBM Plex Sans', sans-serif;
            background-color: #f0f0f8; /* Softer, more textured off-white */
            color: #111133;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'IBM Plex Serif', serif;
        }

        /* Apply small-caps to section headings and FAQ questions */
        h2, h3, h4, h5, h6, .faq-question span {
            font-variant-caps: small-caps;
            letter-spacing: 0.05em; /* Add spacing for readability */
        }

        /* --- Animation & Interactivity --- */
        .animate-on-scroll {
            opacity: 0;
            transform: translateY(30px);
            transition: opacity 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94), transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .animate-on-scroll.is-visible {
            opacity: 1;
            transform: translateY(0);
        }

        .accent-color {
            color: #ffaa00;
        }

        .faq-answer {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.5s ease-in-out, padding-top 0.5s ease-in-out;
        }

        /* --- Spotlight Effect --- */
        .spotlight-container {
            position: relative;
            background: #ffffff;
        }

        .spotlight-container::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            border-radius: 1rem;
            background: radial-gradient(
                    400px circle at var(--mouse-x) var(--mouse-y),
                    rgba(255, 170, 0, 0.08),
                    transparent 80%
            );
            opacity: 0;
            transition: opacity 0.4s ease-out;
        }

        .spotlight-container:hover::before {
            opacity: 1;
        }

        /* --- Parallax Shapes in Hero --- */
        .parallax-shape {
            position: absolute;
            background-color: #ffaa00;
            border-radius: 50%;
            filter: blur(80px);
            will-change: transform;
        }
    </style>
</head>
<body class="antialiased">

<!-- Hero Section -->
<header class="relative h-screen flex items-center justify-center text-center overflow-hidden">
    <!-- Parallax background shapes -->
    <div class="parallax-shape w-64 h-64 top-1/4 left-1/4 opacity-20" data-parallax-speed="0.3"></div>
    <div class="parallax-shape w-48 h-48 top-1/2 right-1/4 opacity-10" data-parallax-speed="0.5"></div>
    <div class="parallax-shape w-32 h-32 bottom-1/4 left-1/3 opacity-10" data-parallax-speed="0.2"></div>

    <div class="relative z-10 p-8 animate-on-scroll is-visible">
        <h1 class="text-5xl sm:text-6xl md:text-8xl font-bold mb-4 bg-gradient-to-r from-[#ffaa00] to-[#111133] bg-clip-text text-transparent pb-2">
            The Art of Typography
        </h1>
        <p class="text-base sm:text-lg md:text-xl max-w-2xl mx-auto uppercase tracking-widest">
            An elevated design experience showcasing the harmony of IBM Plex Serif & Sans.
        </p>
    </div>
    <div class="absolute bottom-10 z-10 animate-pulse">
        <a href="#main-content" class="text-sm tracking-widest">Scroll Down <span class="block text-2xl mt-2">↓</span></a>
    </div>
</header>

<div id="main-content" class="container mx-auto p-4 sm:p-6 md:p-12 max-w-5xl">
    <!-- Main Content Card -->
    <main class="spotlight-container backdrop-blur-xl p-6 sm:p-8 md:p-14 rounded-2xl shadow-2xl border border-gray-200/80">

        <!-- Section 1 -->
        <section class="mb-16 sm:mb-20 animate-on-scroll">
            <h2 class="text-3xl sm:text-4xl font-medium border-b border-slate-200 pb-4 mb-6">Chapter One: The Foundation</h2>
            <div class="prose prose-lg max-w-none prose-p:leading-relaxed">
                <p>
                    This is the standard paragraph text, set in <span class="font-medium" style="color: #111133;">IBM Plex Sans</span>. It's designed for
                    clarity and readability, making it an excellent choice for body copy. Its clean, grotesque-inspired forms are neutral yet friendly,
                    providing a solid foundation for any design. The generous x-height and open counters ensure it performs well on screen and in print.
                </p>
                <p>
                    Notice the contrast between the humanist, approachable feel of the body text and the more structured, classic feel of the headings. This
                    pairing creates a clear visual hierarchy that guides the reader through the content effortlessly. You can also use different weights, like
                    this <strong>bold text</strong> or this <em>italicized text</em> for emphasis.
                </p>
            </div>
        </section>

        <!-- Section 2: Why This Pairing Works -->
        <section class="mb-16 sm:mb-20 animate-on-scroll grid md:grid-cols-5 gap-8 md:gap-12 items-center">
            <div class="md:col-span-3">
                <h3 class="text-3xl font-medium mb-6">Why This Pairing Works</h3>
                <div class="prose prose-lg max-w-none prose-p:leading-relaxed">
                    <p>
                        The combination of a serif font for headings and a sans-serif font for body text is a timeless typographic strategy. The serif font
                        (<span class="font-medium accent-color">Plex Serif</span>) provides elegance and visual authority, drawing the user's eye to important
                        titles. The sans-serif font (<span class="font-medium" style="color: #111133;">Plex Sans</span>) offers high readability for longer
                        passages of text, reducing reader fatigue. This contrast creates a harmonious balance between form and function.
                    </p>
                </div>
            </div>
            <div class="md:col-span-2">
                <blockquote class="border-l-4 pl-6 py-2" style="border-color: #ffaa00;">
                    <p class="text-xl sm:text-2xl italic">"Good typography is like a good servant; it is there to serve the content, not to dominate it."</p>
                    <cite class="text-base not-italic block mt-2">- Robert Bringhurst</cite>
                </blockquote>
            </div>
        </section>

        <!-- Section 3: Real-World Components -->
        <section class="animate-on-scroll">
            <h3 class="text-3xl font-medium mb-12 text-center">A Showcase of Components</h3>
            <div class="space-y-16 sm:space-y-20">

                <!-- Component: Pricing Table -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Plan 1 -->
                    <div class="rounded-2xl shadow-lg border border-gray-200/60 p-8 flex flex-col transition-shadow duration-300 hover:shadow-xl">
                        <h5 class="text-2xl font-semibold mb-2">Starter</h5>
                        <p class="mb-6">For individuals and small projects.</p>
                        <p class="text-5xl font-semibold mb-6" style="font-family: 'IBM Plex Serif', serif;">$19<span class="text-lg font-medium"
                                                                                                                      style="font-family: 'IBM Plex Sans', sans-serif;">/mo</span>
                        </p>
                        <ul class="space-y-3 mb-8 flex-grow">
                            <li class="flex items-center">
                                <svg class="w-5 h-5 mr-2 accent-color" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                10 Projects
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 mr-2 accent-color" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Basic Analytics
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 mr-2 accent-color" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Community Support
                            </li>
                        </ul>
                        <a href="#"
                           class="w-full text-center font-semibold p-3 rounded-lg border-2 border-[#111133] hover:bg-[#111133] hover:text-white transition-colors">Choose
                            Plan</a>
                    </div>
                    <!-- Plan 2 (Featured) -->
                    <div class="bg-[#111133] text-white rounded-2xl shadow-2xl p-8 flex flex-col transform lg:scale-110 z-10 transition-transform duration-300 lg:hover:scale-115">
                        <h5 class="text-2xl font-semibold mb-2">Pro</h5>
                        <p class="mb-6 opacity-80">For professionals and growing teams.</p>
                        <p class="text-5xl font-semibold mb-6" style="font-family: 'IBM Plex Serif', serif;">$49<span class="text-lg font-medium opacity-80"
                                                                                                                      style="font-family: 'IBM Plex Sans', sans-serif;">/mo</span>
                        </p>
                        <ul class="space-y-3 mb-8 flex-grow">
                            <li class="flex items-center">
                                <svg class="w-5 h-5 mr-2 accent-color" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Unlimited Projects
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 mr-2 accent-color" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Advanced Analytics
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 mr-2 accent-color" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Priority Support
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 mr-2 accent-color" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Team Collaboration
                            </li>
                        </ul>
                        <a href="#" class="w-full text-center font-semibold p-3 rounded-lg bg-[#ffaa00] text-[#111133] hover:bg-amber-400 transition-colors">Choose
                            Plan</a>
                    </div>
                    <!-- Plan 3 -->
                    <div class="rounded-2xl shadow-lg border border-gray-200/60 p-8 flex flex-col transition-shadow duration-300 hover:shadow-xl">
                        <h5 class="text-2xl font-semibold mb-2">Enterprise</h5>
                        <p class="mb-6">For large-scale organizations.</p>
                        <p class="text-5xl font-semibold mb-6" style="font-family: 'IBM Plex Serif', serif;">Custom</p>
                        <ul class="space-y-3 mb-8 flex-grow">
                            <li class="flex items-center">
                                <svg class="w-5 h-5 mr-2 accent-color" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Dedicated Infrastructure
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 mr-2 accent-color" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                SLA & 24/7 Support
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 mr-2 accent-color" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Custom Integrations
                            </li>
                        </ul>
                        <a href="#"
                           class="w-full text-center font-semibold p-3 rounded-lg border-2 border-[#111133] hover:bg-[#111133] hover:text-white transition-colors">Contact
                            Us</a>
                    </div>
                </div>

                <!-- Component: FAQ / Accordion -->
                <div class="rounded-2xl shadow-lg border border-gray-200/60 p-8 max-w-3xl mx-auto">
                    <h5 class="text-3xl font-semibold mb-8 text-center">Frequently Asked Questions</h5>
                    <div class="space-y-4" id="faq-accordion">
                        <div class="border-b border-slate-200 pb-4">
                            <button class="faq-question flex justify-between items-center w-full text-left">
                                <span class="text-xl font-semibold">Can I use these fonts for my commercial project?</span>
                                <svg class="w-6 h-6 transition-transform duration-300 shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="faq-answer">
                                <p class="leading-relaxed pt-4">Yes, the IBM Plex font family is licensed under the SIL Open Font License (OFL), which permits
                                    both personal and commercial use completely free of charge. You can use them on your website, in your app, or for print
                                    materials.</p>
                            </div>
                        </div>
                        <div class="border-b border-slate-200 pb-4">
                            <button class="faq-question flex justify-between items-center w-full text-left">
                                <span class="text-xl font-semibold">What makes this font pairing effective?</span>
                                <svg class="w-6 h-6 transition-transform duration-300 shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="faq-answer">
                                <p class="leading-relaxed pt-4">The primary reason is the principle of contrast. The formal, classic structure of IBM Plex Serif
                                    for headings creates a clear visual distinction from the modern, highly-readable IBM Plex Sans used for body text. This
                                    guides the user's eye and makes the content hierarchy immediately obvious.</p>
                            </div>
                        </div>
                        <div class="border-b border-slate-200 pb-4">
                            <button class="faq-question flex justify-between items-center w-full text-left">
                                <span class="text-xl font-semibold">Do I need to host the fonts myself?</span>
                                <svg class="w-6 h-6 transition-transform duration-300 shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="faq-answer">
                                <p class="leading-relaxed pt-4">No, you don't have to. The easiest way to use them is by linking to them from a free service
                                    like Google Fonts, as demonstrated in this document. This ensures fast delivery and that your users always have the latest
                                    version of the font files.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Component: Newsletter Signup -->
                <div class="bg-[#111133] text-white rounded-2xl shadow-xl p-12 max-w-3xl mx-auto text-center">
                    <h5 class="text-3xl font-semibold mb-2">Stay Ahead of the Curve</h5>
                    <p class="text-lg leading-relaxed mb-6 opacity-80">Join our newsletter for the latest insights in design, typography, and technology.</p>
                    <form class="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                        <input type="email" placeholder="<EMAIL>"
                               class="flex-grow p-3 rounded-lg border-2 border-slate-300 focus:ring-2 focus:ring-[#ffaa00] focus:border-[#ffaa00] outline-none transition-shadow"
                               style="color: #111133;">
                        <button type="submit" class="font-semibold p-3 px-6 rounded-lg bg-[#ffaa00] text-[#111133] hover:bg-amber-400 transition-colors">
                            Subscribe
                        </button>
                    </form>
                </div>

            </div>
        </section>

    </main>

    <!-- Footer -->
    <footer class="text-center mt-20 py-10">
        <p class="text-sm">&copy; 2025 Typographica Inc. All rights reserved.</p>
    </footer>

</div>

<script>
    document.addEventListener('DOMContentLoaded', () => {
        // --- Animate on Scroll ---
        const observer = new IntersectionObserver(entries => {
            entries.forEach((entry, index) => {
                if (entry.isIntersecting) {
                    setTimeout(() => {
                        entry.target.classList.add('is-visible');
                    }, index * 100); // Stagger animation
                    observer.unobserve(entry.target);
                }
            });
        }, {
            threshold: 0.1,
        });

        document.querySelectorAll('.animate-on-scroll').forEach(element => {
            observer.observe(element);
        });

        // --- FAQ Accordion ---
        const faqContainer = document.getElementById('faq-accordion');
        if (faqContainer) {
            faqContainer.addEventListener('click', (e) => {
                const questionButton = e.target.closest('.faq-question');
                if (!questionButton) return;

                const answer = questionButton.nextElementSibling;
                const icon = questionButton.querySelector('svg');

                questionButton.parentElement.parentElement.querySelectorAll('.faq-answer').forEach(ans => {
                    if (ans !== answer && ans.style.maxHeight) {
                        ans.style.maxHeight = null;
                        ans.previousElementSibling.querySelector('svg').classList.remove('rotate-180');
                    }
                });

                icon.classList.toggle('rotate-180');
                answer.style.maxHeight = answer.style.maxHeight ? null : answer.scrollHeight + 'px';
            });
        }

        // --- Spotlight Effect ---
        const spotlight = document.querySelector('.spotlight-container');
        if (spotlight) {
            spotlight.addEventListener('mousemove', e => {
                const rect = spotlight.getBoundingClientRect();
                spotlight.style.setProperty('--mouse-x', e.clientX - rect.left + 'px');
                spotlight.style.setProperty('--mouse-y', e.clientY - rect.top + 'px');
            });
        }

        // --- Parallax Effect ---
        const parallaxShapes = document.querySelectorAll('.parallax-shape');
        window.addEventListener('scroll', () => {
            const scrollY = window.scrollY;
            parallaxShapes.forEach(shape => {
                const speed = shape.dataset.parallaxSpeed;
                shape.style.transform = `translateY(${scrollY * speed}px)`;
            });
        });
    });
</script>

</body>
</html>
