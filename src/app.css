@import '@fontsource/ibm-plex-sans/latin-400.css';
@import '@fontsource/ibm-plex-sans/latin-600.css';
@import '@fontsource/ibm-plex-serif/latin-400.css';
@import "tailwindcss";
@import "tw-animate-css";

:root {
    /* Base colors */
    --starry-night-indigo: #111133;
    --solar-flare: #ffaa00;
    --nebula-red: #dc2626;
    --midnight-haze: #5c5c7a;
    --lunar-dust: #ddddee;
    --starlight: #f0f0f8;
    --supernova: #ffffff;

    --body-background: var(--starlight);

    /* Colors */
    --background: var(--supernova);
    --foreground: var(--starry-night-indigo);
    --muted: var(--starlight);
    --muted-foreground: var(--midnight-haze);
    --popover: var(--supernova);
    --popover-foreground: var(--starry-night-indigo);
    --card: var(--supernova);
    --card-foreground: var(--starry-night-indigo);
    --border: var(--lunar-dust);
    --input: var(--lunar-dust);
    --primary: var(--starry-night-indigo);
    --primary-foreground: var(--starlight);
    --secondary: var(--solar-flare);
    --secondary-foreground: var(--starry-night-indigo);
    --accent: var(--starlight);
    --accent-foreground: var(--starry-night-indigo);
    --destructive: var(--nebula-red);
    --destructive-foreground: var(--starlight);
    --ring: var(--midnight-haze);
    --sidebar: var(--starlight);
    --sidebar-foreground: var(--starry-night-indigo);
    --sidebar-primary: var(--starry-night-indigo);
    --sidebar-primary-foreground: var(--starlight);
    --sidebar-accent: var(--lunar-dust);
    --sidebar-accent-foreground: var(--starry-night-indigo);
    --sidebar-border: var(--lunar-dust);
    --sidebar-ring: var(--lunar-dust);

    /* Radius */
    --radius: 0.5rem;
}

@theme inline {
    /* Fonts */
    --font-sans: 'IBM Plex Sans', sans-serif;
    --font-serif: 'IBM Plex Serif', serif;

    /* Only two font weights needed */
    --font-weight-normal: 400;
    --font-weight-medium: 400;
    --font-weight-semibold: 600;
    --font-weight-bold: 600;

    /* Radius (for rounded-*) */
    --radius-sm: calc(var(--radius) - 4px);
    --radius-md: calc(var(--radius) - 2px);
    --radius-lg: var(--radius);
    --radius-xl: calc(var(--radius) + 4px);

    /* Colors */
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);
    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);
    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);
    --color-border: var(--border);
    --color-input: var(--input);
    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);
    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);
    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);
    --color-destructive: var(--destructive);
    --color-destructive-foreground: var(--destructive-foreground);
    --color-ring: var(--ring);
    --color-radius: var(--radius);
    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
    * {
        @apply border-border;
    }

    body {
        background-color: var(--body-background);
        @apply font-sans;
        @apply text-foreground;
    }

    /* All buttons should have a pointer cursor */
    button:not([disabled]),
    [role="button"]:not([disabled]) {
        cursor: pointer;
    }

    input[type="search"]::-webkit-search-decoration,
    input[type="search"]::-webkit-search-cancel-button {
        appearance: none;
    }
}

/* Changes the media query to "dark" class. We do not use the dark class -> no dark theme */
@custom-variant dark (&:where(.dark, .dark *));
