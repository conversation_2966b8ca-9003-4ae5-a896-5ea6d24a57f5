// See https://svelte.dev/docs/kit/types#app.d.ts
// for information about these interfaces
import type { SupabaseClient, Session } from '@supabase/supabase-js';
import type { Database } from '../supabase/types/database.types';

declare global {
	namespace App {
		// interface Error {}
		// interface Locals {}
		interface LayoutData {
			supabase: SupabaseClient<Database>;
			session: Session | null;
			redirectComplete?: boolean;
		}
		// interface PageState {}
		// interface Platform {}
	}
}

export {};
