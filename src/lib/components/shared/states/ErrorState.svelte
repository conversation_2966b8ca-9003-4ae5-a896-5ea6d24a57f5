<script lang="ts">
    import {AlertTriangle, RefreshCw} from '@lucide/svelte';
    // @ts-ignore
    import * as Button from '$lib/components/ui/button';

    interface Props {
        error: string;
        onRetry?: () => void;
    }

    let {
        error,
        onRetry,
    }: Props = $props();
</script>

<div class="col-span-full">
    <div class="flex flex-col items-center justify-center py-16 px-4 text-center">
        <div class="relative max-w-md mx-auto">
            <div class="absolute -inset-16 rounded-full blur-3xl bg-destructive/10"></div>

            <div class="relative z-10 text-center">
                <div class="mb-6 flex justify-center">
                    <AlertTriangle class="size-12 text-destructive"/>
                </div>

                <h3 class="text-xl font-semibold text-foreground mb-2">
                    Something went wrong
                </h3>

                <p class="text-muted-foreground mb-8 max-w-md leading-relaxed">
                    {error}
                </p>

                {#if onRetry}
                    <Button.Root variant="outline" onclick={onRetry}>
                        <RefreshCw class="size-4"/>
                        Try again
                    </Button.Root>
                {/if}
            </div>
        </div>
    </div>
</div>
