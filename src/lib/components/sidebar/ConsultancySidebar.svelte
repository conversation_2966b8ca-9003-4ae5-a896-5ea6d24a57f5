<script lang="ts">
    import SidebarHeader from './SidebarHeader.svelte';
    import UserMenu from './UserMenu.svelte';
    import type {NavigationItem} from './NavigationMenu.svelte';
    import NavigationMenu from './NavigationMenu.svelte';
    // @ts-ignore
    import * as Sidebar from '$lib/components/ui/sidebar';
    // @ts-ignore
    import * as Avatar from '$lib/components/ui/avatar';
    // @ts-ignore
    import * as LucideIcons from '@lucide/svelte';
    import type {ComponentProps} from 'svelte';

    let {
        ref = $bindable(null),
        ...restProps
    }: ComponentProps<typeof Sidebar.Root> = $props();

    const mainNavItems: NavigationItem[] = [
        {
            title: 'Dashboard',
            url: '/dashboard',
            icon: LucideIcons.LayoutDashboard,
        },
        {
            title: 'Clients',
            url: '/clients',
            icon: LucideIcons.Users,
        },
    ];

    const secondaryNavItems: NavigationItem[] = [
        {
            title: 'Support',
            url: '/support',
            icon: LucideIcons.LifeBuoy,
        },
        {
            title: 'Feedback',
            url: '/feedback',
            icon: LucideIcons.Send,
        },
    ];
</script>

<Sidebar.Root collapsible="icon" bind:ref {...restProps}>
    <Sidebar.Header>
        <Sidebar.Menu>
            <Sidebar.MenuItem>
                <Sidebar.MenuButton size="lg">
                    {#snippet child({props})}
                        <a href="/dashboard" {...props}>
                            <Avatar.Root class="size-8 rounded-none items-center">
                                <img src="/logo-icon.png" alt="Logo">
                            </Avatar.Root>
                            <div class="grid">
                                <span class="truncate font-serif uppercase text-secondary">Eunomia</span>
                                <span class="truncate text-xs">Compliance</span>
                            </div>
                        </a>
                    {/snippet}
                </Sidebar.MenuButton>
            </Sidebar.MenuItem>
        </Sidebar.Menu>
    </Sidebar.Header>

    <Sidebar.Content>
        <NavigationMenu items={mainNavItems}/>
        <NavigationMenu items={secondaryNavItems} class="mt-auto"/>
    </Sidebar.Content>

    <Sidebar.Footer>
        <UserMenu/>
    </Sidebar.Footer>
</Sidebar.Root>
