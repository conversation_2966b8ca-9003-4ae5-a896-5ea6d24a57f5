<script lang="ts">
    import UserMenu from './UserMenu.svelte';
    // @ts-ignore
    import * as Sidebar from '$lib/components/ui/sidebar';
    // @ts-ignore
    import * as LucideIcons from '@lucide/svelte';
    import {authStore} from '$lib/stores/authStore';
    import type {ComponentProps} from 'svelte';
    import type {NavigationItem} from './NavigationMenu.svelte';
    import SidebarHeader from '$lib/components/sidebar/SidebarHeader.svelte';
    import NavigationMenu from '$lib/components/sidebar/NavigationMenu.svelte';

    let {
        ref = $bindable(null),
        ...restProps
    }: ComponentProps<typeof Sidebar.Root> = $props();

    const mainNavItems: NavigationItem[] = [
        {
            title: 'Dashboard',
            url: '/dashboard',
            icon: LucideIcons.LayoutDashboard,
        },
        {
            title: 'Queries',
            url: '/queries',
            icon: LucideIcons.SearchCheck,
        },
        {
            title: 'Products',
            url: '/products',
            icon: LucideIcons.Package,
        },
        {
            title: 'Suppliers',
            url: '/suppliers',
            icon: LucideIcons.Building2,
        },
        {
            title: 'Articles',
            url: '/articles',
            icon: LucideIcons.Puzzle,
        },
    ];

    const secondaryNavItems: NavigationItem[] = [
        {
            title: 'Support',
            url: '/support',
            icon: LucideIcons.LifeBuoy,
        },
        {
            title: 'Feedback',
            url: '/feedback',
            icon: LucideIcons.Send,
        },
    ];

    let organizationId = $derived($authStore.profile?.organization_id || undefined);
</script>

<Sidebar.Root collapsible="icon" bind:ref {...restProps}>
    <SidebarHeader title="Compliance Software" organizationId={organizationId} href="/dashboard"/>

    <Sidebar.Content>
        <NavigationMenu items={mainNavItems}/>
        <NavigationMenu items={secondaryNavItems} class="mt-auto"/>
    </Sidebar.Content>

    <Sidebar.Footer>
        <UserMenu/>
    </Sidebar.Footer>
</Sidebar.Root>
