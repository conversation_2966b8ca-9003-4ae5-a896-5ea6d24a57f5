<script lang="ts">
    import SidebarHeader from './SidebarHeader.svelte';
    import type {NavigationItem} from './NavigationMenu.svelte';
    import {authStore} from '$lib/stores/authStore';
    import {canAccessClientSettings} from '$lib/utils/permissions';
    // @ts-ignore
    import * as Sidebar from '$lib/components/ui/sidebar';
    // @ts-ignore
    import * as LucideIcons from '@lucide/svelte';
    import type {ComponentProps} from 'svelte';
    import NavigationMenu from '$lib/components/sidebar/NavigationMenu.svelte';

    interface Props extends ComponentProps<typeof Sidebar.Root> {
        clientId: string;
    }

    let {
        clientId,
        ref = $bindable(null),
        ...restProps
    }: Props = $props();

    const clientUrlPrefix = `/clients/${clientId}`;

    let mainNavItems = $derived.by((): NavigationItem[] => [
        {
            title: 'Dashboard',
            url: `${clientUrlPrefix}/dashboard`,
            icon: LucideIcons.LayoutDashboard,
        },
        {
            title: 'Queries',
            url: `${clientUrlPrefix}/queries`,
            icon: LucideIcons.SearchCheck,
        },
        {
            title: 'Products',
            url: `${clientUrlPrefix}/products`,
            icon: LucideIcons.Package,
        },
        {
            title: 'Suppliers',
            url: `${clientUrlPrefix}/suppliers`,
            icon: LucideIcons.Building2,
        },
        {
            title: 'Articles',
            url: `${clientUrlPrefix}/articles`,
            icon: LucideIcons.Puzzle,
        },
    ]);

    let hasClientSettingsAccess = $derived.by(() => {
        const profile = $authStore.profile;
        return profile ? canAccessClientSettings(profile.role) : false;
    });

    let secondaryNavItems = $derived.by((): NavigationItem[] => {
        const items: NavigationItem[] = [];

        if (hasClientSettingsAccess) {
            items.push({
                title: 'Settings',
                url: `${clientUrlPrefix}/settings/organization/general`,
                icon: LucideIcons.Settings,
            });
        }

        return items;
    });
</script>

<Sidebar.Root collapsible="icon" bind:ref {...restProps}>
    <SidebarHeader title="Managing Client" organizationId={clientId} href="/clients/{clientId}/dashboard"/>

    <Sidebar.Content>
        <NavigationMenu items={mainNavItems}/>
        <NavigationMenu items={secondaryNavItems} class="mt-auto"/>
    </Sidebar.Content>
</Sidebar.Root>
