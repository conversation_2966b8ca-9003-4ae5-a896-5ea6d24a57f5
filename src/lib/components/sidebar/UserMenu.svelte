<script lang="ts">
    import {ChevronsUpDown, LoaderCircle, LogOut, Settings} from '@lucide/svelte';
    import {goto} from '$app/navigation';
    import {authStore} from '$lib/stores/authStore';
    import {canAccessOwnSettings} from '$lib/utils/permissions';
    // @ts-ignore
    import * as Avatar from '$lib/components/ui/avatar';
    // @ts-ignore
    import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
    // @ts-ignore
    import * as Sidebar from '$lib/components/ui/sidebar';
    import {useSidebar} from '$lib/components/ui/sidebar';

    const sidebar = useSidebar();

    let isSigningOut = $state(false);
    let dropdownOpen = $state(false);

    let userFirstName = $derived($authStore.profile?.first_name || undefined);
    let userLastName = $derived($authStore.profile?.last_name || undefined);
    let userEmail = $derived($authStore.user?.email || undefined);
    let hasOwnSettingsAccess = $derived($authStore.profile ? canAccessOwnSettings($authStore.profile.role) : false);

    async function handleSignOut() {
        isSigningOut = true;

        try {
            await authStore.signOut();
            await goto('/auth');
        } finally {
            isSigningOut = false;
            dropdownOpen = false;
        }
    }

    async function handleSettingsClick() {
        await goto('/settings/organization/general');
    }

    function getUserInitials(name: string): string {
        return name
            .split(' ')
            .map(n => n[0])
            .join('')
            .toUpperCase()
            .slice(0, 2);
    }
</script>

<Sidebar.Menu>
    <Sidebar.MenuItem>
        <DropdownMenu.Root bind:open={dropdownOpen}>
            <DropdownMenu.Trigger>
                {#snippet child({props})}
                    <Sidebar.MenuButton
                            size="lg"
                            class="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                            tooltipContent="Profile"
                            {...props}
                    >
                        <Avatar.Root class="size-8 rounded-lg">
                            <Avatar.Fallback class="font-serif rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                                {getUserInitials(userFirstName + ' ' + userLastName)}
                            </Avatar.Fallback>
                        </Avatar.Root>
                        <div class="grid flex-1 text-left text-sm leading-tight">
                            <span class="truncate">Welcome</span>
                            <span class="truncate text-xs">{userFirstName} {userLastName}</span>
                        </div>
                        <ChevronsUpDown class="ml-auto size-4"/>
                    </Sidebar.MenuButton>
                {/snippet}
            </DropdownMenu.Trigger>

            <DropdownMenu.Content
                    class="w-(--bits-dropdown-menu-anchor-width) min-w-56"
                    side={sidebar.isMobile ? "bottom" : "right"}
                    align="end"
                    sideOffset={4}
            >
                <DropdownMenu.Label class="p-0 font-normal">
                    <div class="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                        <Avatar.Root class="size-8 rounded-lg">
                            <Avatar.Fallback class="font-serif rounded-lg">
                                {getUserInitials(userFirstName + ' ' + userLastName)}
                            </Avatar.Fallback>
                        </Avatar.Root>
                        <div class="grid flex-1 text-left text-sm leading-tight">
                            <span class="truncate">{userFirstName} {userLastName}</span>
                            <span class="truncate text-xs">{userEmail}</span>
                        </div>
                    </div>
                </DropdownMenu.Label>

                <DropdownMenu.Separator/>

                {#if hasOwnSettingsAccess}
                    <DropdownMenu.Item onclick={handleSettingsClick}>
                        <Settings/>
                        Settings
                    </DropdownMenu.Item>
                {/if}

                <DropdownMenu.Item onclick={handleSignOut} disabled={isSigningOut}>
                    {#if isSigningOut}
                        <LoaderCircle class="animate-spin"/>
                    {:else}
                        <LogOut/>
                    {/if}
                    Sign out
                </DropdownMenu.Item>
            </DropdownMenu.Content>
        </DropdownMenu.Root>
    </Sidebar.MenuItem>
</Sidebar.Menu>
