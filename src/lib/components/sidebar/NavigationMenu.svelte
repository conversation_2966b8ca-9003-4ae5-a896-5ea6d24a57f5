<script lang="ts">
    // @ts-ignore
    import * as Sidebar from '$lib/components/ui/sidebar';
    import {page} from '$app/state';

    // Local type definitions
    export interface NavigationItem {
        title: string;
        url: string;
        icon?: any;
    }

    let {
        items,
        title = '',
        class: className = '',
    }: {
        items: NavigationItem[];
        title?: string;
        class?: string;
    } = $props();

    // Check if a navigation item is active based on current page
    function isItemActive(item: NavigationItem): boolean {
        return page.url.pathname === item.url;
    }
</script>

{#if items.length > 0}
    <Sidebar.Group class={className}>
        {#if title}
            <Sidebar.GroupLabel class="uppercase">{title}</Sidebar.GroupLabel>
        {/if}
        <Sidebar.Menu>
            {#each items as item (item.title)}
                <Sidebar.MenuItem>
                    <Sidebar.MenuButton tooltipContent={item.title} isActive={isItemActive(item)}>
                        {#snippet child({props})}
                            <a href={item.url} {...props}>
                                {#if item.icon}
                                    {@const IconComponent = item.icon}
                                    <IconComponent/>
                                {/if}
                                <span>{item.title}</span>
                            </a>
                        {/snippet}
                    </Sidebar.MenuButton>
                </Sidebar.MenuItem>
            {/each}
        </Sidebar.Menu>
    </Sidebar.Group>
{/if}
