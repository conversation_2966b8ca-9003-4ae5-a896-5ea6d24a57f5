<script lang="ts">
    // @ts-ignore
    import * as Avatar from '$lib/components/ui/avatar';
    // @ts-ignore
    import * as Sidebar from '$lib/components/ui/sidebar';
    import {supabase} from '$lib/supabaseClient';

    let {
        title,
        organizationId,
        href,
    }: {
        title: string;
        organizationId?: string;
        href: string;
    } = $props();

    let organizationName = $state<string>('');
    let organizationInitials = $state<string>('');

    // Fetch organization data when organizationId changes
    $effect(() => {
        if (organizationId) {
            (async () => {
                try {
                    const {data, error} = await supabase
                        .from('organization')
                        .select('name')
                        .eq('id', organizationId)
                        .single();

                    if (error) throw error;

                    organizationName = data?.name;

                    organizationInitials = organizationName
                        .split(' ')
                        .map(word => word[0])
                        .join('')
                        .toUpperCase()
                        .slice(0, 2);
                } catch (error) {
                    console.error('Error fetching organization:', error);
                }
            })();
        }
    });
</script>

<Sidebar.Header>
    <Sidebar.Menu>
        <Sidebar.MenuItem>
            <Sidebar.MenuButton size="lg">
                {#snippet child({props})}
                    <a {href} {...props}>
                        <Avatar.Root class="size-8 rounded-lg">
                            <Avatar.Fallback class="font-serif rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                                {organizationInitials}
                            </Avatar.Fallback>
                        </Avatar.Root>
                        <div class="grid leading-tight">
                            <span class="truncate">{title}</span>
                            <span class="truncate text-xs">{organizationName || 'Loading...'}</span>
                        </div>
                    </a>
                {/snippet}
            </Sidebar.MenuButton>
        </Sidebar.MenuItem>
    </Sidebar.Menu>
</Sidebar.Header>
