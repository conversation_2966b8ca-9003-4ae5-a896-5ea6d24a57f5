<script lang="ts">
    import {Separator} from '$lib/components/ui/separator';
    // @ts-ignore
    import * as Sidebar from '$lib/components/ui/sidebar';

    interface Props {
        title: string;
    }

    let {title}: Props = $props();
</script>

<header class="flex min-h-14 items-center gap-2 px-4 sticky top-0 z-10 bg-gradient-to-b from-white to-transparent pointer-events-none">
    <Sidebar.Trigger class="-ml-1 pointer-events-auto"/>
    <Separator orientation="vertical" class="mr-2 data-[orientation=vertical]:h-4"/>
    <h1 class="text-lg font-serif">{title}</h1>
</header>
