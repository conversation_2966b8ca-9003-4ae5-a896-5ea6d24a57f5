<script lang="ts">
    import {Skeleton} from '$lib/components/ui/skeleton';
</script>

<div class="space-y-6">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <Skeleton class="h-10 w-full sm:w-80"/>
        <div class="flex items-center gap-2">
            <Skeleton class="h-10 w-32"/>
        </div>
    </div>

    <div class="grid grid-cols-1 gap-4 p-4 border-b">
        <Skeleton class="h-4 w-3/4"/>

    </div>

    <div class="space-y-4">
        {#each Array(5) as _}
            <div class="grid grid-cols-1 gap-4 p-4 border rounded-lg">
                <Skeleton class="h-4 w-full"/>
            </div>
        {/each}
    </div>

    <div class="flex justify-between items-center pt-4">
        <Skeleton class="h-8 w-32"/>
        <div class="flex gap-2">
            <Skeleton class="h-8 w-10 rounded"/>
            <Skeleton class="h-8 w-10 rounded"/>
            <Skeleton class="h-8 w-10 rounded"/>
        </div>
    </div>
</div>
