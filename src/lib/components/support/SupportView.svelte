<script lang="ts">
    import {Skeleton} from '$lib/components/ui/skeleton';
    // @ts-ignore
    import * as Card from '$lib/components/ui/card';
</script>

<div class="space-y-6">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <Skeleton class="h-4 w-64"/>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card.Root>
            <Card.Content class="p-6">
                <div class="text-center space-y-4">
                    <Skeleton class="w-12 h-12 mx-auto rounded-full"/>
                    <Skeleton class="h-6 w-3/4 mx-auto"/>
                    <Skeleton class="h-4 w-full"/>
                    <Skeleton class="h-4 w-5/6 mx-auto mb-4"/>
                    <Skeleton class="h-10 w-full rounded-md"/>
                </div>
            </Card.Content>
        </Card.Root>

        <Card.Root>
            <Card.Content class="p-6">
                <div class="text-center space-y-4">
                    <Skeleton class="w-12 h-12 mx-auto rounded-full"/>
                    <Skeleton class="h-6 w-3/4 mx-auto"/>
                    <Skeleton class="h-4 w-full"/>
                    <Skeleton class="h-4 w-5/6 mx-auto mb-4"/>
                    <Skeleton class="h-10 w-full rounded-md"/>
                </div>
            </Card.Content>
        </Card.Root>

        <Card.Root>
            <Card.Content class="p-6">
                <div class="text-center space-y-4">
                    <Skeleton class="w-12 h-12 mx-auto rounded-full"/>
                    <Skeleton class="h-6 w-3/4 mx-auto"/>
                    <Skeleton class="h-4 w-full"/>
                    <Skeleton class="h-4 w-5/6 mx-auto mb-4"/>
                    <Skeleton class="h-10 w-full rounded-md"/>
                </div>
            </Card.Content>
        </Card.Root>
    </div>
</div>
