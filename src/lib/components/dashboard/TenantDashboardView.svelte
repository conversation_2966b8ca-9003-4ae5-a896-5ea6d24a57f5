<script lang="ts">
    // @ts-ignore
    import * as Card from '$lib/components/ui/card';
    import {Skeleton} from '$lib/components/ui/skeleton';
</script>

<div class="space-y-6">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
            <Skeleton class="h-4 w-64 mb-2"/>
            <Skeleton class="h-3 w-48"/>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card.Root>
            <Card.Content class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <Skeleton class="h-4 w-24 mb-2"/>
                        <Skeleton class="h-8 w-16"/>
                    </div>
                    <Skeleton class="w-8 h-8 rounded-full"/>
                </div>
            </Card.Content>
        </Card.Root>

        <Card.Root>
            <Card.Content class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <Skeleton class="h-4 w-24 mb-2"/>
                        <Skeleton class="h-8 w-16"/>
                    </div>
                    <Skeleton class="w-8 h-8 rounded-full"/>
                </div>
            </Card.Content>
        </Card.Root>

        <Card.Root>
            <Card.Content class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <Skeleton class="h-4 w-24 mb-2"/>
                        <Skeleton class="h-8 w-16"/>
                    </div>
                    <Skeleton class="w-8 h-8 rounded-full"/>
                </div>
            </Card.Content>
        </Card.Root>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card.Root>
            <Card.Header>
                <Skeleton class="h-6 w-32"/>
            </Card.Header>
            <Card.Content class="space-y-4">
                <div class="grid grid-cols-2 gap-4">
                    <div class="p-4 border rounded-lg">
                        <Skeleton class="w-6 h-6 rounded-full mb-2"/>
                        <Skeleton class="h-4 w-24 mb-2"/>
                        <Skeleton class="h-3 w-32"/>
                    </div>
                    <div class="p-4 border rounded-lg">
                        <Skeleton class="w-6 h-6 rounded-full mb-2"/>
                        <Skeleton class="h-4 w-24 mb-2"/>
                        <Skeleton class="h-3 w-32"/>
                    </div>
                    <div class="p-4 border rounded-lg">
                        <Skeleton class="w-6 h-6 rounded-full mb-2"/>
                        <Skeleton class="h-4 w-24 mb-2"/>
                        <Skeleton class="h-3 w-32"/>
                    </div>
                    <div class="p-4 border rounded-lg">
                        <Skeleton class="w-6 h-6 rounded-full mb-2"/>
                        <Skeleton class="h-4 w-24 mb-2"/>
                        <Skeleton class="h-3 w-32"/>
                    </div>
                </div>
            </Card.Content>
        </Card.Root>

        <Card.Root>
            <Card.Header>
                <div class="flex items-center gap-2">
                    <Skeleton class="w-5 h-5 rounded-full"/>
                    <Skeleton class="h-6 w-32"/>
                </div>
            </Card.Header>
            <Card.Content>
                <div class="text-center py-8">
                    <Skeleton class="w-12 h-12 mx-auto rounded-full mb-4"/>
                    <Skeleton class="h-5 w-48 mx-auto mb-2"/>
                    <Skeleton class="h-4 w-64 mx-auto"/>
                </div>
            </Card.Content>
        </Card.Root>
    </div>
</div>
