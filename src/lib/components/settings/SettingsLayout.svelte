<script lang="ts">
    // @ts-ignore
    import * as Sidebar from '$lib/components/ui/sidebar';
    import type {Snippet} from 'svelte';
    import NavigationMenu from '$lib/components/sidebar/NavigationMenu.svelte';
    import type {NavigationItem} from '$lib/components/sidebar/NavigationMenu.svelte';
    // @ts-ignore
    import * as LucideIcons from '@lucide/svelte';
    import {authStore} from '$lib/stores/authStore';
    import {page} from '$app/state';

    interface Props {
        children: Snippet;
    }

    let {children}: Props = $props();

    let isTenant = $derived.by(() => {
        const profile = $authStore.profile;
        return profile?.role.startsWith('tenant_') ?? false;
    });

    let clientId = $derived.by(() => {
        const match = page.url.pathname.match(/^\/clients\/([^\/]+)\/settings(?:\/|$)/);
        return match?.[1] ?? '';
    });

    const clientUrlPrefix = $derived(clientId ? `/clients/${clientId}` : '');

    let organizationNavItems = $derived.by((): NavigationItem[] => [
        {
            title: 'General',
            url: `${clientUrlPrefix}/settings/organization/general`,
            icon: LucideIcons.Building2,
        },
        {
            title: 'Members',
            url: `${clientUrlPrefix}/settings/organization/members`,
            icon: LucideIcons.Users,
        },
        {
            title: 'Billing',
            url: `${clientUrlPrefix}/settings/organization/billing`,
            icon: LucideIcons.CreditCard,
        },
        {
            title: 'Invoices',
            url: `${clientUrlPrefix}/settings/organization/invoices`,
            icon: LucideIcons.Receipt,
        },
    ]);

    let queriesNavItems = $derived.by((): NavigationItem[] => [
        {
            title: 'General',
            url: `${clientUrlPrefix}/settings/queries/general`,
            icon: LucideIcons.SearchCheck,
        },
        {
            title: 'Templates',
            url: `${clientUrlPrefix}/settings/queries/templates`,
            icon: LucideIcons.FileType,
        },
        {
            title: 'Email',
            url: `${clientUrlPrefix}/settings/queries/email`,
            icon: LucideIcons.Mail,
        },
    ]);

    let declarationsNavItems = $derived.by((): NavigationItem[] => [
        {
            title: 'Template',
            url: `${clientUrlPrefix}/settings/declarations/template`,
            icon: LucideIcons.FileText,
        },
    ]);
</script>

<Sidebar.Provider style="--sidebar-width: 10rem;" class="min-h-0">
    <Sidebar.Root variant="floating" class="sticky top-18 h-auto p-0">
        <Sidebar.Content>
            <NavigationMenu items={organizationNavItems} title="Organization"/>

            {#if isTenant || clientId}
                <NavigationMenu items={queriesNavItems} title="Queries"/>
                <NavigationMenu items={declarationsNavItems} title="Declarations"/>
            {/if}
        </Sidebar.Content>
    </Sidebar.Root>

    <Sidebar.Inset class="overflow-clip">
        <div class="pl-4 pt-4">
            {@render children()}
        </div>
    </Sidebar.Inset>
</Sidebar.Provider>
