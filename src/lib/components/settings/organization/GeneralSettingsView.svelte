<script lang="ts">
    import { Skeleton } from '$lib/components/ui/skeleton';
</script>

<div class="space-y-8">
    <div>
        <Skeleton class="h-8 w-48 mb-2" />
        <Skeleton class="h-4 w-80" />
    </div>

    <div class="space-y-6">
        <Skeleton class="h-6 w-32" />
        <div class="space-y-4 rounded-lg border p-4">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div class="space-y-1.5">
                    <Skeleton class="h-4 w-24" />
                    <Skeleton class="h-3 w-48" />
                </div>
                <Skeleton class="h-10 w-full sm:w-1/2" />
            </div>

            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div class="space-y-1.5">
                    <Skeleton class="h-4 w-20" />
                    <Skeleton class="h-3 w-64" />
                </div>
                <Skeleton class="h-10 w-full sm:w-1/2" />
            </div>
        </div>
    </div>

    <div class="space-y-6">
        <Skeleton class="h-6 w-40" />
        <div class="space-y-4 rounded-lg border p-4">
            <div class="flex items-center justify-between">
                <div class="space-y-1.5">
                    <Skeleton class="h-4 w-32" />
                    <Skeleton class="h-3 w-56" />
                </div>
                <Skeleton class="h-7 w-12 rounded-full" />
            </div>

            <div class="flex items-center justify-between">
                <div class="space-y-1.5">
                    <Skeleton class="h-4 w-28" />
                    <Skeleton class="h-3 w-48" />
                </div>
                <Skeleton class="h-7 w-12 rounded-full" />
            </div>
        </div>
    </div>

    <div class="flex justify-end gap-4 pt-4">
        <Skeleton class="h-10 w-24" />
        <Skeleton class="h-10 w-24" />
    </div>
</div>
