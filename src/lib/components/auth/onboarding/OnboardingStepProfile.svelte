<script lang="ts">
    import {Input} from '$lib/components/ui/input';
    import {User, Phone, ArrowRight, LoaderCircle} from '@lucide/svelte';
    import {goto} from '$app/navigation';
    import {supabase} from '$lib/supabaseClient';
    import {authStore} from '$lib/stores/authStore';
    // @ts-ignore
    import * as Form from '$lib/components/ui/form';
    // @ts-ignore
    import * as Select from '$lib/components/ui/select';
    import {profileSchemaOnboarding} from '$lib/utils/validation';
    import {valibot} from 'sveltekit-superforms/adapters';
    import {type Infer, superForm} from 'sveltekit-superforms';
    import {isFieldRequired} from '$lib/utils/form';
    import type {ProfileInsert} from '$lib/types/db.types';

    let isSubmitting = $state(false);

    const superFormInstance = superForm<Infer<typeof profileSchemaOnboarding>>({
        gender: '' as any,
        firstName: '',
        lastName: '',
        phoneNumber: '',
    }, {
        validators: valibot(profileSchemaOnboarding),
        SPA: true,
        invalidateAll: false,
        resetForm: false,
        onUpdate: async ({form: currentFormSnapshot}) => {
            if (!currentFormSnapshot.valid) {
                return;
            }

            await handleSubmit();
        },
    });

    const {
        form: formData,
        enhance: enhance,
    } = superFormInstance;

    // Handle form submission with profile creation
    async function handleSubmit() {
        isSubmitting = true;

        try {
            const userId = $authStore.user?.id;

            // Get organization ID
            const {data: organizationId, error: orgError} = await supabase.rpc('get_my_organization_id');

            if (orgError) {
                throw new Error(`Failed to get organization ID: ${orgError.message}`);
            }

            // Create user profile
            // @ts-ignore
            const profileData: ProfileInsert = {
                id: userId!,
                first_name: $formData.firstName,
                last_name: $formData.lastName,
                gender: $formData.gender as ProfileInsert['gender'],
                phone_number: $formData.phoneNumber,
                organization_id: organizationId,
            };

            const {error: profileError} = await supabase
                .from('profile')
                .insert(profileData);

            if (profileError) throw profileError;

            await authStore.refreshProfile();
            await goto('/dashboard');
        } catch (error) {
            console.error('Profile creation error:', error);
        } finally {
            isSubmitting = false;
        }
    }
</script>

<form method="POST" use:enhance class="space-y-8">
    <!-- Personal Information -->
    <div class="space-y-4">
        <div class="flex items-center gap-2 border-b pb-2 mb-6">
            <User class="size-5 text-primary"/>
            <h3 class="text-base font-serif">Personal Information</h3>
        </div>

        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <Form.Field form={superFormInstance} name="firstName" class="space-y-2">
                <Form.Control>
                    {#snippet children({props})}
                        <Form.Label>
                            First Name
                            {#if isFieldRequired(profileSchemaOnboarding, 'firstName')}
                                <span class="text-red-500">*</span>
                            {/if}
                        </Form.Label>
                        <Input
                                {...props}
                                bind:value={$formData.firstName}
                                placeholder="First name"
                                type="text"
                        />
                    {/snippet}
                </Form.Control>
                <Form.FieldErrors/>
            </Form.Field>

            <Form.Field form={superFormInstance} name="lastName" class="space-y-2">
                <Form.Control>
                    {#snippet children({props})}
                        <Form.Label>
                            Last Name
                            {#if isFieldRequired(profileSchemaOnboarding, 'lastName')}
                                <span class="text-red-500">*</span>
                            {/if}
                        </Form.Label>
                        <Input
                                {...props}
                                bind:value={$formData.lastName}
                                placeholder="Last name"
                                type="text"
                        />
                    {/snippet}
                </Form.Control>
                <Form.FieldErrors/>
            </Form.Field>
        </div>

        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <Form.Field form={superFormInstance} name="gender" class="space-y-2">
                <Form.Control>
                    {#snippet children({props})}
                        <Form.Label>
                            Gender
                            {#if isFieldRequired(profileSchemaOnboarding, 'gender')}
                                <span class="text-red-500">*</span>
                            {/if}
                        </Form.Label>
                        <Select.Root type="single" bind:value={$formData.gender}>
                            <Select.Trigger {...props} class="w-full">
                                {$formData.gender === 'male' ? 'Male' : $formData.gender === 'female' ? 'Female' : $formData.gender === 'other' ? 'Other' : 'Select your gender'}
                            </Select.Trigger>
                            <Select.Content>
                                <Select.Item value="male">Male</Select.Item>
                                <Select.Item value="female">Female</Select.Item>
                                <Select.Item value="other">Other</Select.Item>
                            </Select.Content>
                        </Select.Root>
                    {/snippet}
                </Form.Control>
                <Form.FieldErrors/>
            </Form.Field>

            <Form.Field form={superFormInstance} name="phoneNumber" class="space-y-2">
                <Form.Control>
                    {#snippet children({props})}
                        <Form.Label>
                            Phone Number
                            {#if isFieldRequired(profileSchemaOnboarding, 'phoneNumber')}
                                <span class="text-red-500">*</span>
                            {/if}
                        </Form.Label>
                        <div class="relative">
                            <Input
                                    {...props}
                                    bind:value={$formData.phoneNumber}
                                    placeholder="+****************"
                                    type="tel"
                                    class="pl-10"
                            />
                            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-muted-foreground">
                                <Phone class="size-4"/>
                            </div>
                        </div>
                    {/snippet}
                </Form.Control>
                <Form.FieldErrors/>
            </Form.Field>
        </div>
    </div>

    <Form.Button class="w-full" type="submit" disabled={isSubmitting}>
        Complete Setup
        {#if isSubmitting}
            <LoaderCircle class="animate-spin size-4"/>
        {:else}
            <ArrowRight class="size-4"/>
        {/if}
    </Form.Button>
</form>
