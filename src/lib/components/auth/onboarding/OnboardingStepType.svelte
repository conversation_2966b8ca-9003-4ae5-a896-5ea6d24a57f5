<script lang="ts">
    import {Briefcase, ArrowRight, Building2} from '@lucide/svelte';
    import {Button} from '$lib/components/ui/button';

    let {type = $bindable(), next} = $props();

    function selectType(selectedType: string) {
        type = selectedType;

        next();
    }
</script>

<div class="space-y-6">
    <div class="text-center space-y-2">
        <h2 class="text-lg font-serif">What type of organization are you?</h2>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Consultancy Option -->
        <Button
                variant="outline"
                class="group h-auto p-0 hover:border-orange-300 hover:scale-[1.02] transition-all duration-200"
                onclick={() => selectType('consultancy')}
                aria-label="Select Consultancy"
        >
            <div class="p-5 w-full flex flex-col text-left space-y-3">
                <div class="flex items-center gap-3">
                    <div class="flex-shrink-0 p-3 rounded-full bg-orange-100 group-hover:bg-orange-200 transition-colors duration-200">
                        <Briefcase class="size-6 text-orange-600"/>
                    </div>
                    <h3 class="text-lg font-serif">Consultancy</h3>
                </div>

                <p class="text-muted-foreground text-sm leading-relaxed">
                    Provide compliance services to multiple customers
                </p>

                <div class="flex items-center text-orange-600">
                    <span>Get Started</span>
                    <ArrowRight class="ml-1 size-4"/>
                </div>
            </div>
        </Button>

        <!-- Customer Option -->
        <Button
                variant="outline"
                class="group h-auto p-0 hover:border-blue-300 hover:scale-[1.02] transition-all duration-200"
                onclick={() => selectType('tenant')}
                aria-label="Select Customer"
        >
            <div class="p-5 w-full flex flex-col text-left space-y-3">
                <div class="flex items-center gap-3">
                    <div class="flex-shrink-0 p-3 rounded-full bg-blue-100 group-hover:bg-blue-200 transition-colors duration-200">
                        <Building2 class="size-6 text-blue-600"/>
                    </div>
                    <h3 class="text-lg font-serif">Customer</h3>
                </div>

                <p class="text-muted-foreground text-sm leading-relaxed">
                    Seek compliance services or work independently
                </p>

                <div class="flex items-center text-blue-600">
                    <span>Get Started</span>
                    <ArrowRight class="ml-1 size-4"/>
                </div>
            </div>
        </Button>
    </div>
</div>
