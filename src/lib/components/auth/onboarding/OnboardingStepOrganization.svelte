<script lang="ts">
    import {Input} from '$lib/components/ui/input';
    import {LoaderCircle, Building2, MapPin, ArrowRight, Mail} from '@lucide/svelte';
    // @ts-ignore
    import * as Form from '$lib/components/ui/form';
    // @ts-ignore
    import * as Select from '$lib/components/ui/select';
    import {organizationSchema} from '$lib/utils/validation';
    import type {OrganizationType, Country, OrganizationInsert} from '$lib/types/db.types';
    import {supabase} from '$lib/supabaseClient';
    import {onMount} from 'svelte';
    import {valibot} from 'sveltekit-superforms/adapters';
    import {type Infer, superForm} from 'sveltekit-superforms';
    import {isFieldRequired} from '$lib/utils/form';

    let {
        organizationType,
        next,
    }: {
        organizationType: OrganizationType;
        next: () => void;
    } = $props();

    let countries = $state<Country[]>([]);
    let isLoadingCountries = $state<boolean>(true);
    let isSubmitting = $state<boolean>(false);

    const superFormInstance = superForm<Infer<typeof organizationSchema>>({
        name: '',
        line1: '',
        line2: '',
        postalCode: '',
        city: '',
        state: '',
        country: '',
    }, {
        validators: valibot(organizationSchema),
        SPA: true,
        invalidateAll: false,
        resetForm: false,
        onUpdate: async ({form: currentFormSnapshot}) => {
            if (!currentFormSnapshot.valid) {
                return;
            }

            await handleSubmit();
        },
    });

    const {
        form: formData,
        enhance: enhance,
    } = superFormInstance;

    // Handle organization creation and submission
    async function handleSubmit() {
        isSubmitting = true;

        try {
            // @ts-ignore
            const organizationData: OrganizationInsert = {
                name: $formData.name,
                type: organizationType,
                line1: $formData.line1,
                line2: $formData.line2 || null,
                postal_code: $formData.postalCode || null,
                city: $formData.city,
                state: $formData.state || null,
                country: $formData.country,
            };

            const {error: organizationError} = await supabase
                .from('organization')
                .insert(organizationData);

            if (organizationError) throw organizationError;

            next();
        } catch (error) {
            console.error('Organization creation error:', error);
        } finally {
            isSubmitting = false;
        }
    }

    onMount(async () => {
        try {
            const {data, error} = await supabase
                .from('country')
                .select('code, name')
                .order('name');

            if (error) throw error;
            countries = data || [];
        } catch (error) {
            console.error('Error loading countries:', error);
        } finally {
            isLoadingCountries = false;
        }
    });
</script>

<form method="POST" use:enhance class="space-y-8">
    <div class="space-y-8">
        <div class="flex items-center gap-2 border-b pb-2 mb-6">
            <Building2 class="size-5 text-primary"/>
            <h3 class="text-base font-serif">Organization Information</h3>
        </div>

        <!-- Organization Name -->
        <Form.Field form={superFormInstance} name="name" class="space-y-2">
            <Form.Control>
                {#snippet children({props})}
                    <Form.Label>
                        Organization Name
                        {#if isFieldRequired(organizationSchema, 'name')}
                            <span class="text-red-500">*</span>
                        {/if}
                    </Form.Label>
                    <Input
                            {...props}
                            bind:value={$formData.name}
                            placeholder="Your Organization"
                            type="text"
                    />
                {/snippet}
            </Form.Control>
            <Form.FieldErrors/>
        </Form.Field>
    </div>

    <!-- Address Information -->
    <div class="space-y-4">
        <div class="flex items-center gap-2 border-b pb-2 mb-6">
            <MapPin class="size-5 text-primary"/>
            <h3 class="text-base font-serif">Address Information</h3>
        </div>

        <!-- Street Address -->
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <Form.Field form={superFormInstance} name="line1" class="space-y-2">
                <Form.Control>
                    {#snippet children({props})}
                        <Form.Label>
                            Address Line 1
                            {#if isFieldRequired(organizationSchema, 'line1')}
                                <span class="text-red-500">*</span>
                            {/if}
                        </Form.Label>
                        <Input
                                {...props}
                                bind:value={$formData.line1}
                                placeholder="Street address"
                                type="text"
                        />
                    {/snippet}
                </Form.Control>
                <Form.FieldErrors/>
            </Form.Field>

            <Form.Field form={superFormInstance} name="line2" class="space-y-2">
                <Form.Control>
                    {#snippet children({props})}
                        <Form.Label>
                            Address Line 2
                            {#if isFieldRequired(organizationSchema, 'line2')}
                                <span class="text-red-500">*</span>
                            {/if}
                        </Form.Label>
                        <Input
                                {...props}
                                bind:value={$formData.line2}
                                placeholder="Apartment, suite, etc."
                                type="text"
                        />
                    {/snippet}
                </Form.Control>
                <Form.FieldErrors/>
            </Form.Field>
        </div>

        <!-- City and Postal Code -->
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <Form.Field form={superFormInstance} name="city" class="space-y-2">
                <Form.Control>
                    {#snippet children({props})}
                        <Form.Label>
                            City
                            {#if isFieldRequired(organizationSchema, 'city')}
                                <span class="text-red-500">*</span>
                            {/if}
                        </Form.Label>
                        <Input
                                {...props}
                                bind:value={$formData.city}
                                placeholder="City"
                                type="text"
                        />
                    {/snippet}
                </Form.Control>
                <Form.FieldErrors/>
            </Form.Field>

            <Form.Field form={superFormInstance} name="postalCode" class="space-y-2">
                <Form.Control>
                    {#snippet children({props})}
                        <Form.Label>
                            Postal Code / ZIP
                            {#if isFieldRequired(organizationSchema, 'postalCode')}
                                <span class="text-red-500">*</span>
                            {/if}
                        </Form.Label>
                        <Input
                                {...props}
                                bind:value={$formData.postalCode}
                                placeholder="Postal code"
                                type="text"
                        />
                    {/snippet}
                </Form.Control>
                <Form.FieldErrors/>
            </Form.Field>
        </div>

        <!-- State/Region and Country -->
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <Form.Field form={superFormInstance} name="state" class="space-y-2">
                <Form.Control>
                    {#snippet children({props})}
                        <Form.Label>
                            State / Region / Province
                            {#if isFieldRequired(organizationSchema, 'state')}
                                <span class="text-red-500">*</span>
                            {/if}
                        </Form.Label>
                        <Input
                                {...props}
                                bind:value={$formData.state}
                                placeholder="State or province"
                                type="text"
                        />
                    {/snippet}
                </Form.Control>
                <Form.FieldErrors/>
            </Form.Field>

            <Form.Field form={superFormInstance} name="country" class="space-y-2">
                <Form.Control>
                    {#snippet children({props})}
                        <Form.Label>
                            Country
                            {#if isFieldRequired(organizationSchema, 'country')}
                                <span class="text-red-500">*</span>
                            {/if}
                        </Form.Label>
                        <Select.Root type="single" bind:value={$formData.country}>
                            <Select.Trigger {...props} class="w-full">
                                {#if isLoadingCountries}
                                    <LoaderCircle class="animate-spin size-4"/>
                                {:else}
                                    {$formData.country ? countries.find(country => country.code === $formData.country)?.name || 'Select a country' : 'Select a country'}
                                {/if}
                            </Select.Trigger>
                            <Select.Content>
                                {#each countries as country}
                                    <Select.Item value={country.code}>{country.name}</Select.Item>
                                {/each}
                            </Select.Content>
                        </Select.Root>
                    {/snippet}
                </Form.Control>
                <Form.FieldErrors/>
            </Form.Field>
        </div>
    </div>

    <Form.Button class="w-full" type="submit" disabled={isSubmitting}>
        Continue
        {#if isSubmitting}
            <LoaderCircle class="animate-spin size-4"/>
        {:else}
            <ArrowRight class="size-4"/>
        {/if}
    </Form.Button>
</form>
