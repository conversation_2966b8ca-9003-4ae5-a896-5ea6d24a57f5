<script lang="ts">
    import {supabase} from '$lib/supabaseClient';
    // @ts-ignore
    import * as Card from '$lib/components/ui/card';
    import {LoaderCircle, CheckCircle2, PencilLine} from '@lucide/svelte';
    import OnboardingStepType from './OnboardingStepType.svelte';
    import OnboardingStepOrganization from './OnboardingStepOrganization.svelte';
    import OnboardingStepProfile from './OnboardingStepProfile.svelte';
    import {onMount} from 'svelte';
    import type {OrganizationType} from '$lib/types/db.types';

    interface StepConfig {
        id: string;
        name: string;
        completed: boolean;
    }

    let currentStep = $state(0);
    let isLoading = $state(true);
    let organizationType = $state<OrganizationType | undefined>();

    let stepConfig = $state<StepConfig[]>([
        {id: 'type', name: 'Type', completed: false},
        {id: 'organization', name: 'Organization', completed: false},
        {id: 'profile', name: 'Profile', completed: false},
    ]);

    function getStepById(id: string): StepConfig | undefined {
        return stepConfig.find(step => step.id === id);
    }

    function markStepCompleted(id: string): void {
        const step = getStepById(id);
        if (step) {
            step.completed = true;
        }
    }

    function getCurrentStep(): StepConfig | undefined {
        return stepConfig[currentStep];
    }

    function setCurrentStepToProfile(): void {
        const profileStepIndex = stepConfig.findIndex(step => step.id === 'profile');
        currentStep = profileStepIndex >= 0 ? profileStepIndex : stepConfig.length - 1;
    }

    // Check for existing organization on mount
    onMount(async () => {
        try {
            // Check if user already has an organization
            const {data: orgId, error: orgError} = await supabase.rpc('get_my_organization_id');

            if (orgError) {
                console.error('Error checking organization:', orgError);
            } else if (orgId) {
                // Mark type and organization steps as completed and skip to profile
                markStepCompleted('type');
                markStepCompleted('organization');

                // Set current step to profile step
                setCurrentStepToProfile();
            }
        } catch (error) {
            console.error('Error fetching users organization information:', error);
        } finally {
            isLoading = false;
        }
    });

    function nextStep(): void {
        const currentStepData = getCurrentStep();
        if (!currentStepData) {
            console.error('No current step found');
            return;
        }

        // Mark current step as completed
        markStepCompleted(currentStepData.id);

        // Move to next step if available
        const nextStepIndex = currentStep + 1;
        if (nextStepIndex < stepConfig.length) {
            currentStep = nextStepIndex;
        }
    }
</script>

<Card.Card class="max-w-4xl mx-auto">
    <Card.CardHeader class="pb-4 border-b">
        <div class="text-center space-y-1">
            <Card.CardTitle class="text-2xl font-serif">Complete Your Setup</Card.CardTitle>
            <Card.CardDescription class="text-base text-muted-foreground">
                Let's get your account ready in just a few steps
            </Card.CardDescription>
        </div>
    </Card.CardHeader>

    <Card.CardContent class="pt-6">
        <div class="flex justify-between items-center mb-12 px-4">
            {#each stepConfig as step, index}
                {@const isActive = index === currentStep}
                {@const isComplete = step.completed || index < currentStep}

                <div class="flex flex-col items-center relative">
                    <div class="size-12 rounded-full border-2 {isActive ? 'border-primary bg-primary text-white' : isComplete ? 'bg-green-500 text-white border-green-500' : 'border-muted bg-white text-muted-foreground'} flex items-center justify-center mb-3">
                        {#if isComplete}
                            <CheckCircle2 class="size-6"/>
                        {:else if isActive}
                            <PencilLine class="size-6"/>
                        {:else}
                            <span class="text-md font-serif">{index + 1}</span>
                        {/if}
                    </div>
                    <span class="text-sm {isActive ? 'text-primary' : isComplete ? 'text-green-600' : 'text-muted-foreground'} text-center">
                            {step.name}
                        </span>
                </div>

                {#if index < stepConfig.length - 1}
                    <div class="flex-1 h-1 self-start mt-6 mx-6 rounded-full {isComplete ? 'bg-green-500' : 'bg-muted'}"></div>
                {/if}
            {/each}
        </div>

        {#if isLoading}
            <div class="flex flex-col items-center justify-center py-12">
                <LoaderCircle class="animate-spin size-10"/>
            </div>
        {:else}
            <!-- Step Content -->
            {#if getCurrentStep()?.id === 'type'}
                <OnboardingStepType
                        bind:type={organizationType}
                        next={nextStep}
                />
            {:else if getCurrentStep()?.id === 'organization' && organizationType}
                <OnboardingStepOrganization
                        organizationType={organizationType}
                        next={nextStep}
                />
            {:else if getCurrentStep()?.id === 'profile'}
                <OnboardingStepProfile/>
            {/if}
        {/if}
    </Card.CardContent>
</Card.Card>
