<script lang="ts">
    import {supabase} from '$lib/supabaseClient';
    import {Input} from '$lib/components/ui/input';
    // @ts-ignore
    import * as Form from '$lib/components/ui/form';
    // @ts-ignore
    import * as Card from '$lib/components/ui/card';
    import {authSchema} from '$lib/utils/validation';
    import {valibot} from 'sveltekit-superforms/adapters';
    import {superForm, type Infer} from 'sveltekit-superforms';
    import OtpForm from './OtpForm.svelte';
    import {toast} from 'svelte-sonner';
    import {LoaderCircle, Mail} from '@lucide/svelte';

    let isSubmitting = $state<boolean>(false);
    let otpSent = $state<boolean>(false);
    let userEmail = $state<string>('');

    const superFormInstance = superForm<Infer<typeof authSchema>>(
        {email: ''},
        {
            validators: valibot(authSchema),
            SPA: true,
            invalidateAll: false,
            resetForm: false,
            onUpdate: async ({form: currentFormSnapshot}) => {
                if (!currentFormSnapshot.valid) {
                    return;
                }

                isSubmitting = true;
                userEmail = currentFormSnapshot.data.email;

                const {error} = await supabase.auth.signInWithOtp({
                    email: userEmail,
                    options: {
                        shouldCreateUser: true,
                    },
                });

                isSubmitting = false;

                if (error) {
                    if (error.status === 429) {
                        toast.error('Too many requests. Please try again later.');
                    } else {
                        toast.error(error.message);
                    }
                } else {
                    otpSent = true;
                }
            },
        },
    );

    const {
        form: formData,
        enhance: enhance,
    } = superFormInstance;
</script>

{#if !otpSent}
    <Card.Card class="max-w-md mx-auto">
        <Card.CardHeader class="pb-4 text-center space-y-2">
            <Card.CardTitle class="text-3xl font-serif">Welcome</Card.CardTitle>
            <Card.CardDescription class="text-base text-muted-foreground">
                Sign in to your account or create a new one
            </Card.CardDescription>
        </Card.CardHeader>

        <Card.CardContent>
            <form method="POST" use:enhance class="space-y-6">
                <Form.Field form={superFormInstance} name="email" class="space-y-3">
                    <Form.Control>
                        {#snippet children({props})}
                            <Form.Label>Email Address</Form.Label>
                            <Input
                                    {...props}
                                    bind:value={$formData.email}
                                    placeholder="<EMAIL>"
                                    autocomplete="email"
                                    type="email"
                                    class="h-12 px-4"
                            />
                        {/snippet}
                    </Form.Control>
                    <Form.FieldErrors/>
                </Form.Field>

                <Form.Button class="w-full h-12 text-base" disabled={isSubmitting}>
                    {#if isSubmitting}
                        <LoaderCircle class="animate-spin size-5"/>
                    {:else}
                        <Mail class="size-5"/>
                    {/if}

                    Continue with Email
                </Form.Button>

                <p class="text-center text-sm text-muted-foreground">
                    By continuing, you agree to our
                    <a href="/terms" class="text-primary hover:text-muted-foreground underline">Terms of Service</a>
                    and
                    <a href="/privacy" class="text-primary hover:text-muted-foreground underline">Privacy Policy</a>.
                </p>
            </form>
        </Card.CardContent>
    </Card.Card>
{:else}
    <OtpForm userEmail={userEmail}/>
{/if}
