<script lang="ts">
    import {goto} from '$app/navigation';
    import {supabase} from '$lib/supabaseClient';
    // @ts-ignore
    import * as Form from '$lib/components/ui/form';
    // @ts-ignore
    import * as Card from '$lib/components/ui/card';
    import {Separator} from '$lib/components/ui/separator';
    // @ts-ignore
    import * as InputOTP from '$lib/components/ui/input-otp';
    import {otpSchema} from '$lib/utils/validation';
    import {valibot} from 'sveltekit-superforms/adapters';
    import {superForm, type Infer} from 'sveltekit-superforms';
    import {toast} from 'svelte-sonner';
    import {LoaderCircle} from '@lucide/svelte';

    let {
        userEmail,
    }: {
        userEmail: string;
    } = $props();

    let isSubmitting = $state(false);
    let lastSubmittedOtp = $state('');

    const superFormInstance = superForm<Infer<typeof otpSchema>>(
        {otp: ''},
        {
            validators: valibot(otpSchema),
            SPA: true,
            invalidateAll: false,
            resetForm: false,
            onUpdate: async ({form: currentFormSnapshot}) => {
                if (!currentFormSnapshot.valid) {
                    return;
                }

                isSubmitting = true;
                lastSubmittedOtp = currentFormSnapshot.data.otp;

                const {error} = await supabase.auth.verifyOtp({
                    email: userEmail,
                    token: currentFormSnapshot.data.otp,
                    type: 'email',
                });

                if (error) {
                    isSubmitting = false;

                    if ('otp_expired' === error.code) {
                        toast.error('The verification code is invalid or has expired. Please try again.');
                    } else {
                        toast.error(error.message);
                    }
                } else {
                    await goto('/dashboard');
                }
            },
        },
    );

    const {
        form: formData,
        enhance: enhance,
    } = superFormInstance;

    $effect(() => {
        const currentOtp = $formData.otp;
        if (currentOtp?.length === 6 && !isSubmitting && currentOtp !== lastSubmittedOtp) {
            superFormInstance.submit();
        }
    });
</script>

<Card.Card class="max-w-md mx-auto relative">
    <Card.CardHeader class="pb-4 text-center space-y-2">
        <Card.CardTitle class="text-3xl font-serif">Check Your Email</Card.CardTitle>
        <Card.CardDescription class="text-base text-muted-foreground">
            We've sent a 6-digit verification code to<br>
            <strong class="text-foreground">{userEmail}</strong>
        </Card.CardDescription>
    </Card.CardHeader>

    <Card.CardContent>
        <form method="POST" use:enhance class="space-y-8">
            <Form.Field form={superFormInstance} name="otp" class="space-y-2">
                <Form.Control>
                    {#snippet children({props})}
                        <div class="flex flex-col items-center space-y-4">
                            <Form.Label class="text-center">Enter Verification Code</Form.Label>
                            <InputOTP.Root maxlength={6} {...props} bind:value={$formData.otp} autofocus>
                                {#snippet children({cells})}
                                    <InputOTP.Group class="gap-2">
                                        {#each cells as cell (cell)}
                                            <InputOTP.Slot {cell} class="border-1"/>
                                        {/each}
                                    </InputOTP.Group>
                                {/snippet}
                            </InputOTP.Root>
                        </div>
                    {/snippet}
                </Form.Control>
                <Form.FieldErrors class="text-center"/>
            </Form.Field>

            <p class="text-center text-sm text-muted-foreground">
                Didn't receive the code? Check your spam folder or try again.
            </p>
        </form>

        {#if isSubmitting}
            <div class="absolute rounded-xl inset-0 flex justify-center items-center bg-white/30 backdrop-blur-xs z-10">
                <div class="flex flex-col items-center space-y-3">
                    <LoaderCircle class="animate-spin size-10"/>
                </div>
            </div>
        {/if}
    </Card.CardContent>
</Card.Card>
