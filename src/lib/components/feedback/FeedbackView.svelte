<script lang="ts">
    import {Skeleton} from '$lib/components/ui/skeleton';
    // @ts-ignore
    import * as Card from '$lib/components/ui/card';
</script>

<div class="space-y-6">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
            <Skeleton class="h-4 w-72"/>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card.Root>
            <Card.Header>
                <Card.Title class="flex items-center gap-2">
                    <Skeleton class="w-5 h-5 rounded-full"/>
                    <Skeleton class="h-6 w-32"/>
                </Card.Title>
            </Card.Header>
            <Card.Content class="space-y-4">
                <div>
                    <Skeleton class="h-4 w-20 mb-1"/>
                    <Skeleton class="h-10 w-full rounded-md"/>
                </div>

                <div>
                    <Skeleton class="h-4 w-24 mb-1"/>
                    <Skeleton class="min-h-[120px] w-full rounded-md"/>
                </div>
            </Card.Content>
        </Card.Root>

        <Card.Root>
            <Card.Header>
                <Card.Title class="flex items-center gap-2">
                    <Skeleton class="w-5 h-5 rounded-full"/>
                    <Skeleton class="h-6 w-40"/>
                </Card.Title>
            </Card.Header>
            <Card.Content class="space-y-4">
                <Skeleton class="h-4 w-full"/>
                <Skeleton class="h-4 w-5/6 mx-auto"/>
                <div class="flex justify-center space-x-2">
                    <Skeleton class="w-8 h-8 rounded-full"/>
                    <Skeleton class="w-8 h-8 rounded-full"/>
                    <Skeleton class="w-8 h-8 rounded-full"/>
                    <Skeleton class="w-8 h-8 rounded-full"/>
                    <Skeleton class="w-8 h-8 rounded-full"/>
                </div>
                <div class="text-center">
                    <Skeleton class="h-3 w-48 mx-auto mb-4"/>
                </div>
            </Card.Content>
        </Card.Root>
    </div>
</div>
