<script lang="ts">
    import { Skeleton } from '$lib/components/ui/skeleton';
    // @ts-ignore
    import * as Button from '$lib/components/ui/button';

    interface Props {
        count?: number;
    }

    let { count = 6 }: Props = $props();
</script>

<div class="contents relative">
    {#each Array(count) as _, index (index)}
        <Button.Root
                variant="outline"
                class="group h-auto p-0 w-full"
                disabled
        >
            <div class="p-4 w-full flex flex-col text-left space-y-3">
                <!-- Header with Avatar, Name and Location -->
                <div class="flex items-start gap-3">
                    <Skeleton class="size-11 rounded-lg flex-shrink-0" />

                    <div class="flex-1 min-w-0 space-y-1">
                        <!-- Name skeleton - text-base with leading-tight -->
                        <Skeleton class="h-[20px] w-3/4" />

                        <!-- Location with proper spacing -->
                        <div class="h-[20px] flex items-center">
                            <Skeleton class="w-3 h-3 mr-1 flex-shrink-0" />
                            <!-- Location text - text-sm -->
                            <Skeleton class="h-[16px] w-1/2" />
                        </div>
                    </div>
                </div>

                <!-- Action area -->
                <div class="h-6 flex items-center">
                    <!-- Action text - text-sm -->
                    <Skeleton class="h-[16px] w-16" />
                    <Skeleton class="ml-1 size-3" />
                </div>
            </div>
        </Button.Root>
    {/each}
</div>

<!-- Fade overlay -->
<div class="col-span-full pointer-events-none relative -mt-32 h-32 bg-gradient-to-t from-background to-transparent"></div>
