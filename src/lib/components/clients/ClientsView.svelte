<script lang="ts">
    import {onMount} from 'svelte';
    import {toast} from 'svelte-sonner';
    import {Building2, SearchX, Plus, X} from '@lucide/svelte';
    import ClientCard from './ClientCard.svelte';
    import ClientCardSkeleton from './ClientCardSkeleton.svelte';
    import ClientsSearchBar from './ClientsSearchBar.svelte';
    import ClientsToolbar from './ClientsToolbar.svelte';
    import ErrorState from '$lib/components/shared/states/ErrorState.svelte';
    import EmptyState from '$lib/components/shared/states/EmptyState.svelte';
    import {
        clientsStore,
        filtersStore,
        filteredClients,
        clientsActions,
    } from '$lib/stores/clientsStore';

    // Reactive state from stores
    $: ({isLoading, error} = $clientsStore);
    $: filters = $filtersStore;
    $: clients = $filteredClients;

    // Event handlers
    function handleSearchChange(value: string) {
        clientsActions.setSearchQuery(value);
    }

    function handleSearchClear() {
        clientsActions.setSearchQuery('');
    }

    function handleRefresh() {
        clientsActions.fetchClients(true);
    }

    function handleSortChange(sortBy: typeof filters.sortBy, sortOrder: typeof filters.sortOrder) {
        clientsActions.setSorting(sortBy, sortOrder);
    }

    function handleRetry() {
        clientsActions.fetchClients(true);
    }

    function handleInviteClient() {
        // TODO: Implement invite client functionality
        toast.info('Invite client functionality coming soon!');
    }

    // Initialize data on mount
    onMount(() => {
        clientsActions.fetchClients();
    });
</script>

<div class="space-y-6">
    <!-- Search and Toolbar -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <ClientsSearchBar
                bind:value={filters.searchQuery}
                onValueChange={handleSearchChange}
                onClear={handleSearchClear}
                disabled={isLoading}
        />

        <ClientsToolbar
                {isLoading}
                clientsCount={clients.length}
                {filters}
                onRefresh={handleRefresh}
                onSortChange={handleSortChange}
                onAddClient={handleInviteClient}
        />
    </div>

    <!-- Client Cards Grid -->
    <div class="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-6 relative">
        {#if error}
            <!-- Skeleton background for error state -->
            <ClientCardSkeleton count={6}/>
            <!-- Error state overlay -->
            <div class="absolute inset-0 flex items-center justify-center">
                <ErrorState
                        error="Failed to load clients"
                        onRetry={handleRetry}
                />
            </div>
        {:else if isLoading}
            <ClientCardSkeleton count={6}/>
        {:else if clients.length === 0}
            <!-- Skeleton background for empty state -->
            <ClientCardSkeleton count={6}/>
            <!-- Empty state overlay -->
            <div class="absolute inset-0 flex items-center justify-center">
                {#if filters.searchQuery}
                    <EmptyState
                            icon={SearchX}
                            title="No clients found"
                            description="No match for your search criteria."
                            action={{
                                label: 'Clear search',
                                icon: X,
                                onclick: handleSearchClear
                            }}
                    />
                {:else}
                    <EmptyState
                            icon={Building2}
                            title="No clients yet"
                            description="Start by inviting your first client."
                            action={{
                                label: 'Invite Client',
                                icon: Plus,
                                onclick: handleInviteClient
                            }}
                    />
                {/if}
            </div>
        {:else}
            {#each clients as client (client.id)}
                <ClientCard {client}/>
            {/each}
        {/if}
    </div>
</div>
