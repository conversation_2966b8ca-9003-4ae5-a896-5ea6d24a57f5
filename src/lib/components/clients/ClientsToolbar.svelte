<script lang="ts">
    import { RefreshCw, SortAsc, SortDesc, Plus } from '@lucide/svelte';
    // @ts-ignore
    import * as Button from '$lib/components/ui/button';
    // @ts-ignore
    import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
    import type { ClientsFilters } from '$lib/stores/clientsStore';

    interface Props {
        isLoading?: boolean;
        clientsCount?: number;
        filters: ClientsFilters;
        onRefresh?: () => void;
        onSortChange?: (sortBy: ClientsFilters['sortBy'], sortOrder: ClientsFilters['sortOrder']) => void;
        onAddClient?: () => void;
    }

    let {
        isLoading = false,
        clientsCount = 0,
        filters,
        onRefresh,
        onSortChange,
        onAddClient,
    }: Props = $props();

    const sortOptions = [
        { value: 'name', label: 'Name' },
        { value: 'city', label: 'City' },
        { value: 'country', label: 'Country' },
        { value: 'created_at', label: 'Date Added' },
    ] as const;

    function handleSortChange(sortBy: ClientsFilters['sortBy']) {
        const newOrder = filters.sortBy === sortBy && filters.sortOrder === 'asc' ? 'desc' : 'asc';
        onSortChange?.(sortBy, newOrder);
    }

    let currentSortLabel = $derived.by(() => {
        const option = sortOptions.find(opt => opt.value === filters.sortBy);
        return option?.label || 'Name';
    });
</script>

<div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
    <div class="flex items-center gap-2 text-sm text-muted-foreground">
        <span>
            {clientsCount} {clientsCount === 1 ? 'client' : 'clients'}
        </span>
    </div>

    <div class="flex items-center gap-2">
        <!-- Refresh Button -->
        <Button.Root
            variant="outline"
            size="sm"
            onclick={onRefresh}
            disabled={isLoading}
            aria-label="Refresh clients"
        >
            <RefreshCw class="h-4 w-4 {isLoading ? 'animate-spin' : ''}" />
            <span class="hidden sm:inline ml-2">Refresh</span>
        </Button.Root>

        <!-- Sort Dropdown -->
        <DropdownMenu.Root>
            <DropdownMenu.Trigger>
                {#snippet child({props})}
                    <Button.Root
                        variant="outline"
                        size="sm"
                        aria-label="Sort options"
                        {...props}
                    >
                        {#if filters.sortOrder === 'asc'}
                            <SortAsc class="h-4 w-4" />
                        {:else}
                            <SortDesc class="h-4 w-4" />
                        {/if}
                        <span class="hidden sm:inline ml-2">Sort by {currentSortLabel}</span>
                    </Button.Root>
                {/snippet}
            </DropdownMenu.Trigger>

            <DropdownMenu.Content align="end" class="w-48">
                <DropdownMenu.Label>Sort by</DropdownMenu.Label>
                <DropdownMenu.Separator />

                {#each sortOptions as option}
                    <DropdownMenu.Item
                        onclick={() => handleSortChange(option.value)}
                        class="flex items-center justify-between"
                    >
                        <span>{option.label}</span>
                        {#if filters.sortBy === option.value}
                            {#if filters.sortOrder === 'asc'}
                                <SortAsc class="h-3 w-3" />
                            {:else}
                                <SortDesc class="h-3 w-3" />
                            {/if}
                        {/if}
                    </DropdownMenu.Item>
                {/each}
            </DropdownMenu.Content>
        </DropdownMenu.Root>

        <!-- Invite Client Button -->
        {#if onAddClient}
            <Button.Root onclick={onAddClient} size="sm">
                <Plus class="h-4 w-4" />
                <span class="hidden sm:inline ml-2">Invite Client</span>
            </Button.Root>
        {/if}
    </div>
</div>
