import type { Organization } from '$lib/types/db.types';

/**
 * Generate initials from organization name
 */
export function getOrganizationInitials(name: string): string {
    return name
        .split(' ')
        .map(word => word[0])
        .join('')
        .toUpperCase()
        .slice(0, 2);
}

/**
 * Format organization location (city, country) for display
 */
export function formatOrganizationLocation(organization: Organization): string {
    const parts = [organization.city, organization.country].filter(Boolean);
    return parts.join(', ');
}

/**
 * Check if organization is recently created (within last 7 days)
 */
export function isRecentlyCreated(organization: Organization): boolean {
    const createdDate = new Date(organization.created_at);
    const weekAgo = new Date();
    weekAgo.setDate(weekAgo.getDate() - 7);

    return createdDate > weekAgo;
}
