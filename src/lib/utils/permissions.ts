import type {Role} from '$lib/types/db.types';

/**
 * Check if a role has permission to access own organization settings
 */
export function canAccessOwnSettings(role: Role): boolean {
    return role === 'consultancy_manager' || role === 'tenant_manager';
}

/**
 * Check if a role has permission to access client settings
 */
export function canAccessClientSettings(role: Role): boolean {
    return role === 'consultancy_manager';
}
