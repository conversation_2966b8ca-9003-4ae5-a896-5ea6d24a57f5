import type { ObjectSchema } from 'valibot';

/**
 * Helper function to check if a field is required based on a Valibot schema
 * @param schema - The Valibot object schema
 * @param fieldName - The name of the field to check
 * @returns true if the field is required, false if optional
 */
export function isFieldRequired(schema: ObjectSchema<any, any>, fieldName: string): boolean {
    const schemaEntries = schema.entries;
    const fieldSchema = schemaEntries[fieldName];

    if (!fieldSchema) return false;

    // Check if the field is wrapped in an OptionalSchema
    return (fieldSchema as any).type !== 'optional';
}
