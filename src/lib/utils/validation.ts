import {
  object,
  string,
  email,
  minLength,
  pipe,
  optional,
  picklist,
  digits
} from 'valibot';
import { Constants } from '../../../supabase/types/database.types';

// Auth form validation schema
export const authSchema = object({
  email: pipe(string(), email('Please enter a valid email address')),
});

// OTP form validation schema
export const otpSchema = object({
  otp: pipe(
      string(),
      minLength(6, 'Please enter 6 digits'),
      digits('Please enter digits only')
  )
});

// Organization form validation schema
export const organizationSchema = object({
  name: pipe(string(), minLength(1, 'Organization name is required')),
  line1: pipe(string(), minLength(1, 'Address line 1 is required')),
  line2: optional(string()),
  postalCode: optional(string()),
  city: pipe(string(), minLength(1, 'City is required')),
  state: optional(string()),
  country: pipe(string(), minLength(1, 'Country is required'))
});

// Profile form validation schema
export const profileSchemaOnboarding = object({
  gender: picklist(Constants.public.Enums.gender, "Select gender"),
  firstName: pipe(string(), minLength(1, 'First name is required')),
  lastName: pipe(string(), minLength(1, 'Last name is required')),
  phoneNumber: pipe(string(), minLength(1, 'Phone number is required')),
});
