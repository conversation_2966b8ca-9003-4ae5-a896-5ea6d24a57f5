import type {Database} from '../../../supabase/types/database.types';

// Re-export the database types for easier imports
export * from '../../../supabase/types/database.types';

// Re-export types from the database schema
export type Profile = Database['public']['Tables']['profile']['Row'];
export type Country = Database['public']['Tables']['country']['Row'];
export type Organization = Database['public']['Tables']['organization']['Row'];

// Insert types for forms
export type ProfileInsert = Database['public']['Tables']['profile']['Insert'];
export type OrganizationInsert = Database['public']['Tables']['organization']['Insert'];

// Enums
export type OrganizationType = Database['public']['Enums']['organization_type'];
export type Role = Database['public']['Enums']['role'];
