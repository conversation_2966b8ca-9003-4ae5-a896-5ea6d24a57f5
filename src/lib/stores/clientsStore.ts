import { writable, derived, get } from 'svelte/store';
import { supabase } from '$lib/supabaseClient';
import type { Organization } from '$lib/types/db.types';
import { toast } from 'svelte-sonner';

export interface ClientsState {
    clients: Organization[];
    isLoading: boolean;
    error: string | null;
    searchQuery: string;
    lastFetched: Date | null;
}

export interface ClientsFilters {
    searchQuery: string;
    sortBy: 'name' | 'city' | 'country' | 'created_at';
    sortOrder: 'asc' | 'desc';
}

const initialState: ClientsState = {
    clients: [],
    isLoading: false,
    error: null,
    searchQuery: '',
    lastFetched: null,
};

const initialFilters: ClientsFilters = {
    searchQuery: '',
    sortBy: 'name',
    sortOrder: 'asc',
};

// Create the main store
const clientsStore = writable<ClientsState>(initialState);
const filtersStore = writable<ClientsFilters>(initialFilters);

// Derived store for filtered and sorted clients
export const filteredClients = derived(
    [clientsStore, filtersStore],
    ([$clients, $filters]) => {
        let filtered = [...$clients.clients];

        // Apply search filter
        if ($filters.searchQuery.trim()) {
            const query = $filters.searchQuery.toLowerCase().trim();
            filtered = filtered.filter(client => 
                client.name.toLowerCase().includes(query) ||
                client.city.toLowerCase().includes(query) ||
                client.country.toLowerCase().includes(query) ||
                client.line1.toLowerCase().includes(query) ||
                (client.line2 && client.line2.toLowerCase().includes(query)) ||
                (client.postal_code && client.postal_code.toLowerCase().includes(query)) ||
                (client.state && client.state.toLowerCase().includes(query))
            );
        }

        // Apply sorting
        filtered.sort((a, b) => {
            let aValue: string | Date;
            let bValue: string | Date;

            switch ($filters.sortBy) {
                case 'name':
                    aValue = a.name.toLowerCase();
                    bValue = b.name.toLowerCase();
                    break;
                case 'city':
                    aValue = a.city.toLowerCase();
                    bValue = b.city.toLowerCase();
                    break;
                case 'country':
                    aValue = a.country.toLowerCase();
                    bValue = b.country.toLowerCase();
                    break;
                case 'created_at':
                    aValue = new Date(a.created_at);
                    bValue = new Date(b.created_at);
                    break;
                default:
                    aValue = a.name.toLowerCase();
                    bValue = b.name.toLowerCase();
            }

            if (aValue < bValue) return $filters.sortOrder === 'asc' ? -1 : 1;
            if (aValue > bValue) return $filters.sortOrder === 'asc' ? 1 : -1;
            return 0;
        });

        return filtered;
    }
);

// Store actions
export const clientsActions = {
    // Fetch clients from the database
    async fetchClients(force = false): Promise<void> {
        const currentState = get(clientsStore);
        
        // Skip if already loading or recently fetched (unless forced)
        if (currentState.isLoading) return;
        if (!force && currentState.lastFetched && 
            Date.now() - currentState.lastFetched.getTime() < 30000) { // 30 seconds cache
            return;
        }

        clientsStore.update(state => ({
            ...state,
            isLoading: true,
            error: null,
        }));

        try {
            const { data, error } = await supabase
                .from('organization')
                .select('*')
                .eq('type', 'tenant')
                .order('name');

            //throw "UUUH";
            if (error) throw error;

            clientsStore.update(state => ({
                ...state,
                clients: data || [],
                isLoading: false,
                lastFetched: new Date(),
                error: null,
            }));
        } catch (error: any) {
            console.error('Error fetching clients:', error);
            
            clientsStore.update(state => ({
                ...state,
                isLoading: false,
                error: error.message || 'Failed to fetch clients',
            }));

            toast.error('Failed to load clients. Please try again.');
        }
    },

    // Update search query
    setSearchQuery(query: string): void {
        filtersStore.update(filters => ({
            ...filters,
            searchQuery: query,
        }));
    },

    // Update sorting
    setSorting(sortBy: ClientsFilters['sortBy'], sortOrder: ClientsFilters['sortOrder']): void {
        filtersStore.update(filters => ({
            ...filters,
            sortBy,
            sortOrder,
        }));
    },

    // Clear all filters
    clearFilters(): void {
        filtersStore.set(initialFilters);
    },

    // Reset store to initial state
    reset(): void {
        clientsStore.set(initialState);
        filtersStore.set(initialFilters);
    },
};

// Export stores
export { clientsStore, filtersStore };
