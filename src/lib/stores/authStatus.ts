import { derived } from 'svelte/store';
import { authStore, type AuthState } from './authStore';

/**
 * Enum representing the detailed, computed authentication states.
 */
export enum AuthStatus {
    /** Initial loading state before auth state is determined by Supabase */
    INITIALIZING = 'INITIALIZING',
    /** User is not authenticated */
    UNAUTHENTICATED = 'UNAUTHENTICATED',
    /** User is authenticated (user object exists) but profile is still being loaded or re-loaded */
    AUTHENTICATED_LOADING_PROFILE = 'AUTHENTICATED_LOADING_PROFILE',
    /** User is authenticated, profile fetch is complete, but no profile data was found */
    AUTHENTICATED_NO_PROFILE = 'AUTHENTICATED_NO_PROFILE',
    /** User is fully authenticated and profile data is loaded */
    AUTHENTICATED = 'AUTHENTICATED'
}

/**
 * A derived Svelte store that computes the current AuthStatus
 * based on the state of the main authStore.
 */

export const authStatus = derived(
    authStore, // The source store
    ($authStoreState: AuthState): AuthStatus => {
        if ($authStoreState.isLoading) {
            if ($authStoreState.user) {
                return AuthStatus.AUTHENTICATED_LOADING_PROFILE;
            } else {
                return AuthStatus.INITIALIZING;
            }
        } else {
            if ($authStoreState.user) {
                if ($authStoreState.profile) {
                    return AuthStatus.AUTHENTICATED;
                } else {
                    return AuthStatus.AUTHENTICATED_NO_PROFILE;
                }
            } else {
                return AuthStatus.UNAUTHENTICATED;
            }
        }
    }
);
