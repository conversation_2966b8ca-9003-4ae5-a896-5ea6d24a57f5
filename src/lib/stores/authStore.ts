import {writable, get} from 'svelte/store';
import {supabase} from '$lib/supabaseClient';
import type {User, Subscription, Session, AuthChangeEvent} from '@supabase/supabase-js';
import type {Profile} from '$lib/types/db.types';

/**
 * Represents the state of the authentication store.
 * isLoading is true during initial app load OR when fetching/refreshing profile.
 */
export type AuthState = {
    user: User | null;
    profile: Profile | null;
    isLoading: boolean;
};

/**
 * Creates and manages the main authentication store.
 * It handles user data, profile fetching, and loading states.
 */
const createAuthStore = () => {
    const initialState: AuthState = {
        user: null,
        profile: null,
        isLoading: true,
    };

    const store = writable<AuthState>(initialState);

    let isInitialized = false;
    let authSubscription: Subscription | null = null;

    /**
     * Fetches a user's profile from the database.
     * @param userId - The ID of the user whose profile to fetch.
     * @returns The user's profile or null if not found or on error.
     */
    async function fetchProfile(userId: string): Promise<Profile | null> {
        const {data: profileData, error} = await supabase
            .from('profile')
            .select('*')
            .eq('id', userId)
            .single();

        if (error) {
            return null;
        }

        return profileData;
    }

    /**
     * Initializes the auth store and sets up auth state change listeners.
     */
    function initialize(): () => void {
        if (isInitialized) {
            return () => {
                // No-op
            };
        }
        isInitialized = true;
        let previousSessionId: string | null = null;

        const {data: {subscription}} = supabase.auth.onAuthStateChange(
            async (event: AuthChangeEvent, session: Session | null) => {
                const currentSessionId = session?.user?.id || null;
                const isNewSession = currentSessionId !== previousSessionId;
                previousSessionId = currentSessionId;

                if (session?.user) {
                    store.update(s => ({
                        ...s,
                        user: session.user,
                    }));

                    if (event === 'SIGNED_IN' && isNewSession) {
                        refreshProfile().catch(error => {
                            console.error(`Error during refreshProfile call, event ${event}:`, error);
                        });
                    }
                } else {
                    store.set({user: null, profile: null, isLoading: false});
                }
            },
        );

        authSubscription = subscription;

        return () => {
            if (authSubscription) {
                authSubscription.unsubscribe();
                authSubscription = null;
            }
            isInitialized = false;
        };
    }

    /**
     * Signs the user out.
     */
    async function signOut(): Promise<void> {
        const {error} = await supabase.auth.signOut();

        if (error) {
            console.error('Error signing out:', error.message);
            throw error;
        }
    }

    /**
     * Refreshes the current user's profile data from the database.
     */
    async function refreshProfile(): Promise<void> {
        store.update(s => ({
            ...s,
            isLoading: true,
        }));

        const user = get(store).user;

        if (!user) {
            return;
        }

        const fetchedProfile = await fetchProfile(user.id);

        store.update(s => ({
            ...s,
            profile: fetchedProfile,
            isLoading: false,
        }));
    }

    /**
     * Wait for the auth store to reach a stable state (isLoading is false)
     */
    async function waitForStableState(): Promise<void> {
        const currentState = get(store);
        if (!currentState.isLoading) {
            return Promise.resolve();
        }

        return new Promise((resolve) => {
            const unsubscribe = store.subscribe(state => {
                if (!state.isLoading) {
                    unsubscribe();
                    resolve();
                }
            });
        });
    }

    return {
        subscribe: store.subscribe,
        initialize,
        signOut,
        refreshProfile,
        waitForStableState,
    };
};

export const authStore = createAuthStore();
