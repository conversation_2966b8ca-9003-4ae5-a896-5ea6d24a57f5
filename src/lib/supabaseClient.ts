import {createClient, type SupabaseClient} from '@supabase/supabase-js';
import {PUBLIC_SUPABASE_URL, PUBLIC_SUPABASE_ANON_KEY} from '$env/static/public';
import type {Database} from '../../supabase/types/database.types';
import {browser} from '$app/environment';
import {goto} from '$app/navigation';

type SupabaseError = {
    code?: string;
    message: string;
    details?: string;
    hint?: string;
};

type SupabaseResponse<T = unknown> = {
    data: T | null;
    error: SupabaseError | null;
};

type QueryMethod = 'select' | 'insert' | 'update' | 'delete' | 'upsert';

// JWT error codes that should trigger automatic sign out
const JWT_ERROR_CODES = ['PGRST300', 'PGRST301', 'PGRST302'] as const;

/**
 * Type guard to check if an error is a JWT authentication error
 */
function isJWTError(error: SupabaseError): boolean {
    return error.code ? JWT_ERROR_CODES.includes(error.code as typeof JWT_ERROR_CODES[number]) : false;
}

/**
 * Type guard to check if a response contains an error
 */
function hasError<T>(response: SupabaseResponse<T>): response is SupabaseResponse<T> & { error: SupabaseError } {
    return response.error !== null;
}

/**
 * Handle JWT authentication errors by signing out the user
 */
async function handleJWTError(error: SupabaseError): Promise<void> {
    if (supabaseClient) {
        await supabaseClient.auth.signOut();
    }

    await goto('/auth');
}

let supabaseClient: SupabaseClient<Database> | null = null;

if (browser) {
    supabaseClient = createClient<Database>(PUBLIC_SUPABASE_URL, PUBLIC_SUPABASE_ANON_KEY, {
        auth: {
            persistSession: true,
            storageKey: 'auth',
        },
    });

    // Intercept all Supabase responses automatically for error handling
    if (supabaseClient) {
        const originalFrom = supabaseClient.from.bind(supabaseClient);

        supabaseClient.from = <T extends keyof Database['public']['Tables']>(table: T) => {
            const queryBuilder = originalFrom(table);

            // Create a wrapper function for query methods
            const wrapQueryMethod = (method: QueryMethod) => {
                if (queryBuilder[method]) {
                    const originalMethod = (queryBuilder as any)[method].bind(queryBuilder);

                    (queryBuilder as any)[method] = (...args: any[]) => {
                        const query = originalMethod(...args);

                        // Wrap the promise to handle JWT errors
                        const originalThen = query.then.bind(query);
                        query.then = (
                            onFulfilled?: ((value: SupabaseResponse) => any) | null,
                            onRejected?: ((reason: unknown) => any) | null,
                        ) => {
                            return originalThen((result: SupabaseResponse) => {
                                // Check for JWT errors and handle them automatically
                                if (hasError(result) && isJWTError(result.error)) {
                                    handleJWTError(result.error);
                                }

                                return onFulfilled ? onFulfilled(result) : result;
                            }, onRejected);
                        };

                        return query;
                    };
                }
            };

            // Wrap all query methods with proper typing
            (['select', 'insert', 'update', 'delete', 'upsert'] as const).forEach(wrapQueryMethod);

            return queryBuilder;
        };
    }
}

/**
 * Export the configured Supabase client
 * This client includes automatic JWT error handling
 */
export const supabase = supabaseClient as SupabaseClient<Database>;
