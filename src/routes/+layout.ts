import {browser} from '$app/environment';
import {redirect} from '@sveltejs/kit';
import type {LayoutLoad} from './$types';
import {supabase} from '$lib/supabaseClient';
import {AuthStatus, authStatus} from '$lib/stores/authStatus';
import {authStore} from '$lib/stores/authStore';
import {get} from 'svelte/store';

export const load: LayoutLoad = async ({url}) => {
    // Skip during SSR
    if (!browser) {
        return;
    }

    await authStore.waitForStableState();

    // Handle root path redirects
    if (url.pathname === '/') {
        const currentAuthStatus = get(authStatus);

        if (currentAuthStatus === AuthStatus.AUTHENTICATED) {
            throw redirect(302, '/dashboard');
        }

        if (currentAuthStatus === AuthStatus.AUTHENTICATED_NO_PROFILE) {
            throw redirect(302, '/onboarding');
        }

        throw redirect(302, '/auth');
    }

    // Get session for layout data
    const {data} = await supabase.auth.getSession();
    return {
        supabase,
        session: data.session,
        redirectComplete: true,
    };
};
