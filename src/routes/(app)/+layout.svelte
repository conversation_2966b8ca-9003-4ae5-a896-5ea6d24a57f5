<script lang="ts">
    import ConsultancySidebar from '$lib/components/sidebar/ConsultancySidebar.svelte';
    import ConsultancyClientSidebar from '$lib/components/sidebar/ConsultancyClientSidebar.svelte';
    import TenantSidebar from '$lib/components/sidebar/TenantSidebar.svelte';
    import PageHeader from '$lib/components/layout/PageHeader.svelte';
    // @ts-ignore
    import * as Sidebar from '$lib/components/ui/sidebar';
    import {page} from '$app/state';
    import {authStore} from '$lib/stores/authStore';
    import {themeColor} from '$lib/stores/themeStore';
    import type {Snippet} from 'svelte';

    interface Props {
        children: Snippet;
    }

    let {children}: Props = $props();

    const PAGE_TITLES: Record<string, string> = {
        '/dashboard': 'Dashboard',
        '/clients': 'Clients',
        '/support': 'Support',
        '/feedback': 'Feedback',
        '/queries': 'Queries',
        '/products': 'Products',
        '/suppliers': 'Suppliers',
        '/articles': 'Articles',
    };

    let isConsultancy = $derived.by(() => {
        const profile = $authStore.profile;
        return profile?.role.startsWith('consultancy_') ?? false;
    });

    let isClientManagement = $derived.by(() => {
        const pathname = page.url.pathname;
        return pathname.startsWith('/clients/') && pathname !== '/clients';
    });

    let clientId = $derived.by(() => {
        const match = page.url.pathname.match(/^\/clients\/([^\/]+)/);
        return match?.[1] ?? '';
    });

    let isSettingsPage = $derived.by(() => {
        const pathname = page.url.pathname;
        return pathname.includes('/settings/');
    });

    let currentPageTitle = $derived.by(() => {
        const pathname = page.url.pathname;

        if (isSettingsPage) {
            return 'Settings';
        }

        if (isClientManagement) {
            const subPath = pathname.replace(`/clients/${clientId}`, '') || '/dashboard';
            return PAGE_TITLES[subPath] || 'Dashboard';
        }

        return PAGE_TITLES[pathname] ?? '...';
    });

    $effect(() => {
        if (isConsultancy && !isClientManagement) {
            $themeColor = '#ffffff';
        } else {
            $themeColor = '#f0f0f8';
        }
    });
</script>

<style>
    /* Dark consultancy sidebar */
    :global(.outer-sidebar-wrapper > [data-slot="sidebar"][data-variant="floating"]) {
        --sidebar: var(--starry-night-indigo);
        --sidebar-foreground: var(--starlight);
        --sidebar-primary: var(--starlight);
        --sidebar-primary-foreground: var(--starry-night-indigo);
        --sidebar-accent: var(--midnight-haze);
        --sidebar-accent-foreground: var(--starlight);
        --sidebar-border: var(--starry-night-indigo);
        --sidebar-ring: var(--midnight-haze);
    }

    /* Floating sidebars should have same border radius as content inset, no shadow and no border */
    :global([data-slot="sidebar"][data-variant="floating"] [data-slot="sidebar-inner"]) {
        border-radius: calc(var(--radius) + 4px);
        box-shadow: none;
        border: none;
    }

    /* Mobile Edge-to-Edge view should have padding for the gesture bar */
    :global(.outer-sidebar-wrapper) {
        padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
    }
</style>

<Sidebar.Provider open={!isClientManagement && !isSettingsPage} class="outer-sidebar-wrapper">
    {#if isConsultancy}
        <ConsultancySidebar variant="floating"/>
    {:else}
        <TenantSidebar variant="inset"/>
    {/if}

    {#if isConsultancy && isClientManagement}
        <Sidebar.Inset>
            <Sidebar.Provider open={!isSettingsPage}>
                <ConsultancyClientSidebar {clientId} variant="inset" class="sticky"/>

                <Sidebar.Inset class="overflow-clip">
                    <PageHeader title={currentPageTitle}/>
                    <div class="p-4">
                        {@render children()}
                    </div>
                </Sidebar.Inset>
            </Sidebar.Provider>
        </Sidebar.Inset>
    {:else}
        <Sidebar.Inset class="overflow-clip">
            <PageHeader title={currentPageTitle}/>
            <div class="p-4">
                {@render children()}
            </div>
        </Sidebar.Inset>
    {/if}
</Sidebar.Provider>
