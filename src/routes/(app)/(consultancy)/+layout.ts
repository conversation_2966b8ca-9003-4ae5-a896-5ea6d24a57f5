import {browser} from '$app/environment';
import {redirect} from '@sveltejs/kit';
import type {LayoutLoad} from './$types';
import {authStore} from '$lib/stores/authStore';
import {get} from 'svelte/store';

export const load: LayoutLoad = async () => {
    // Skip during SSR
    if (!browser) {
        return {};
    }

    await authStore.waitForStableState();

    const authState = get(authStore);
    const profile = authState.profile;

    if (profile && profile.role.startsWith('tenant_')) {
        // Redirect tenant users to their dashboard
        throw redirect(302, '/dashboard');
    }

    return {};
};
