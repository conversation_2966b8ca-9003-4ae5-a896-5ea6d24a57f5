import {browser} from '$app/environment';
import {redirect} from '@sveltejs/kit';
import type {LayoutLoad} from './$types';
import {authStore} from '$lib/stores/authStore';
import {get} from 'svelte/store';
import {canAccessClientSettings} from '$lib/utils/permissions';

export const load: LayoutLoad = async () => {
    // Skip during SSR
    if (!browser) {
        return {};
    }

    await authStore.waitForStableState();

    const authState = get(authStore);
    const profile = authState.profile;

    // Check if user has permission to access client settings
    if (!profile || !canAccessClientSettings(profile.role)) {
        throw redirect(302, '/dashboard');
    }

    return {};
};
