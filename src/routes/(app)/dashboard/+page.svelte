<script lang="ts">
    import TenantDashboardView from '$lib/components/dashboard/TenantDashboardView.svelte';
    import ConsultancyDashboardView from '$lib/components/dashboard/ConsultancyDashboardView.svelte';
    import {authStore} from '$lib/stores/authStore';

    let isConsultancy = $derived.by(() => {
        const profile = $authStore.profile;

        return profile?.role.startsWith('consultancy_');
    });
</script>

{#if isConsultancy}
    <ConsultancyDashboardView/>
{:else}
    <TenantDashboardView/>
{/if}
