import { browser } from '$app/environment';
import { redirect } from '@sveltejs/kit';
import type { LayoutLoad } from './$types';
import { AuthStatus, authStatus } from '$lib/stores/authStatus';
import { authStore } from '$lib/stores/authStore';
import { get } from 'svelte/store';

export const load: LayoutLoad = async () => {
  // Skip during SSR
  if (!browser) {
    return {};
  }

  await authStore.waitForStableState();

  const currentAuthStatus = get(authStatus);

  if (currentAuthStatus === AuthStatus.AUTHENTICATED) {
    throw redirect(302, '/dashboard');
  }

  if (currentAuthStatus === AuthStatus.AUTHENTICATED_NO_PROFILE) {
    throw redirect(302, '/onboarding');
  }

  return {};
};
