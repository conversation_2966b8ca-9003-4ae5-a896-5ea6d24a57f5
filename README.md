# Compliance Software

A comprehensive application for managing supplier queries for product compliance in the European Union.

The structure is similar to tax consultancy software, where management consultancies handle the queries while clients enter the data.

The system also supports clients working independently without a management consultancy.

# Features

### Authentication & User Management
- User signup and login (both only via OTP, no passwords)
- Multi-step onboarding process
- Member invitation system

### Role-Based Access Control
- Two organization types: consultancy and tenant
- Consultancy roles: Manager, Employee
- Tenant roles: Manager, Compliance, Purchaser, Seller
- Permission-based access to features and data

### Organization Management
- Create and manage organizations (consultancies and tenants)
- Link tenants to consultancies for managed relationships
- Address and contact information management

### Profile Management
- User profiles with personal information
- Role assignment within organizations
- Gender, contact details, and other personal information

# Technical Architecture

### Frontend
- SvelteKit (Svelte 5). Docs: https://svelte.dev/docs/kit
- shadcn-svelte (UI Components). Docs: https://shadcn-svelte.com/docs
- @lucide/svelte (Icons). Docs: https://lucide.dev/icons/
- Tailwind CSS v4 (Styling). Docs: https://tailwindcss.com/docs
- Sveltekit Superforms (Form Handling). Docs: https://superforms.rocks/
- Valibot (Schema Validation). Docs: https://valibot.dev
- TypeScript (Type Safety). Docs: https://www.typescriptlang.org/docs
- pnpm (Package Manager). Docs: https://pnpm.io

### Backend
- Supabase (Database, Authentication, Storage, Edge Functions). Docs: https://supabase.com/docs
- PostgreSQL database with Row-Level Security (RLS)
- Authentication system with email verification (OTP)
- Edge Functions for server-side logic

### Database Structure
- Organizations table (consultancies and tenants)
- Profiles table (user information)
- Permissions and role-permission mappings
- Invitation system for adding new members

# Deployment & Development

### Deployment
- SvelteKit is built in full static mode (no server)
- Hosted on a CDN (Bunny CDN)

### Development Notes
- Always use KISS as a core principle for writing and refactoring code.
- Always use 4 spaces for indentation.
- Always write proper Typescript code (with types).
- When importing multiple components, use the 'import * as x' notation. Add @ts-ignore because of WebStorm bug.
- shadcn-svelte uses 'onclick' instead of 'on:click'.
- Do not touch shadcn-svelte components under src/lib/components/ui.
- Database migrations are managed through Supabase migrations.
- Do not use text for "Loading...", use LoaderCircle (Lucide).
- Do not run "pnpm dev" for Augment Code.
- The 'page' import from '$app/stores' is deprecated. Use import { page } from '$app/state'.
- Use the isFieldRequired method and an asterisks for required fields.
- Component names have to be in UpperCamelCase format.
- Always check the code after changing anything (pnpm run check).
- Do not create index.ts files for easy importing of components.
- Do not use tailwind gray colors, use the app.css colors instead.

### Project Structure

- You can get a full overview of the files in the project by running: git ls-files --cached --others --exclude-standard | sort
