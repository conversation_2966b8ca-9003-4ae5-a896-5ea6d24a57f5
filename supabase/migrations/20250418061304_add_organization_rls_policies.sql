-- ----------------------------------------
-- Permissions
-- ----------------------------------------

INSERT INTO public.permission (name) VALUES
   ('organization_update'),
   ('organization_delete'),
   ('organization_retrieve_tenants');


INSERT INTO public.role_permission (role, permission) VALUES
   ('consultancy_manager', 'organization_retrieve_tenants'),
   ('tenant_manager', 'organization_update'),
   ('tenant_manager', 'organization_delete');


-- ----------------------------------------
-- RLS <PERSON>es
-- ----------------------------------------

CREATE POLICY "Allow retrieve own organization"
    ON public.organization
    FOR SELECT
    TO authenticated
    USING (
        id = public.get_my_organization_id()
    );


CREATE POLICY "Allow retrieve tenant organizations"
    ON public.organization
    FOR SELECT
    TO authenticated
    USING (
        public.check_permission('organization_retrieve_tenants') AND
        type = 'tenant' AND
        consultancy_organization_id = public.get_my_organization_id()
    );


CREATE POLICY "Allow create organization"
    ON public.organization
    FOR INSERT
    TO authenticated
    WITH CHECK (
        -- Allow if user has no organization yet (first-time onboarding)
        public.get_my_organization_id() IS NULL
    );


CREATE POLICY "Allow update own organization"
    ON public.organization
    FOR UPDATE
    TO authenticated
    USING (
        public.check_permission('organization_update') AND
        id = public.get_my_organization_id()
    )
    WITH CHECK (
        public.check_permission('organization_update') AND
        id = public.get_my_organization_id()
    );


CREATE POLICY "Allow delete own organization"
    ON public.organization
    FOR DELETE
    TO authenticated
    USING (
        public.check_permission('organization_delete') AND
        id = public.get_my_organization_id()
    );
