-- ----------------------------------------
-- Functions
-- ----------------------------------------

-- Function for checking if a role is valid for an organization type
CREATE OR REPLACE FUNCTION public.is_role_valid_for_organization_type(
    p_organization_id UUID,
    p_role public.role
)
RETURNS BOOLEAN AS $$
DECLARE
    v_org_type public.organization_type;
    v_role_text TEXT;
BEGIN
    -- Early return for NULL values
    IF p_organization_id IS NULL OR p_role IS NULL THEN
        RETURN TRUE;
    END IF;

    PERFORM 1 FROM public.organization WHERE id = p_organization_id;

    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;

    -- Get organization type with a direct query
    SELECT type INTO v_org_type
    FROM public.organization
    WHERE id = p_organization_id;

    -- Convert role to text once
    v_role_text := p_role::text;

    -- Check role prefix
    RETURN CASE
        WHEN v_org_type = 'consultancy' THEN v_role_text LIKE 'consultancy\_%'
        WHEN v_org_type = 'tenant' THEN v_role_text LIKE 'tenant\_%'
        ELSE FALSE
    END;
END;
$$ LANGUAGE plpgsql STABLE;

COMMENT ON FUNCTION public.is_role_valid_for_organization_type(UUID, public.role)
IS 'Checks if the organizations role prefix matches the organization type (consultancy/tenant).';


-- Function to get the highest role for an organization type
CREATE OR REPLACE FUNCTION public.get_highest_role_for_organization_type(
    p_organization_type public.organization_type
)
RETURNS public.role AS $$
BEGIN
    RETURN CASE
        WHEN p_organization_type = 'consultancy' THEN 'consultancy_manager'::public.role
        WHEN p_organization_type = 'tenant' THEN 'tenant_manager'::public.role
        ELSE NULL
    END;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

COMMENT ON FUNCTION public.get_highest_role_for_organization_type(public.organization_type)
IS 'Returns the highest role for a given organization type (consultancy_manager for consultancy, tenant_manager for tenant).';


-- Function to check if an organization has at least one user with the highest role
CREATE OR REPLACE FUNCTION public.organization_has_highest_role_user(
    p_organization_id UUID
)
RETURNS BOOLEAN AS $$
DECLARE
    v_org_type public.organization_type;
    v_highest_role public.role;
    v_count INTEGER;
BEGIN
    -- Get organization type
    SELECT type INTO v_org_type
    FROM public.organization
    WHERE id = p_organization_id;

    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;

    -- Get the highest role for this organization type
    v_highest_role := public.get_highest_role_for_organization_type(v_org_type);

    -- Count users with the highest role
    SELECT COUNT(*) INTO v_count
    FROM public.profile
    WHERE organization_id = p_organization_id
    AND role = v_highest_role;

    RETURN v_count > 0;
END;
$$ LANGUAGE plpgsql STABLE;

COMMENT ON FUNCTION public.organization_has_highest_role_user(UUID)
IS 'Checks if an organization has at least one user with the highest role for that organization type.';


-- ----------------------------------------
-- Tables
-- ----------------------------------------

CREATE TABLE public.profile (
    id                  UUID PRIMARY KEY REFERENCES auth.users (id) ON DELETE CASCADE,
    first_name          TEXT NOT NULL,
    last_name           TEXT NOT NULL,
    gender              public.gender NOT NULL,
    phone_number        TEXT NOT NULL,
    organization_id     UUID NOT NULL REFERENCES public.organization (id) ON DELETE CASCADE,
    role                public.role, -- Initially nullable to allow trigger to set it

    CONSTRAINT check_profile_role_matches_organization_type CHECK (
        public.is_role_valid_for_organization_type(organization_id, role)
    )
);
CREATE INDEX idx_profile_organization_id ON public.profile USING btree (organization_id);
ALTER TABLE public.profile ENABLE ROW LEVEL SECURITY;
COMMENT ON TABLE public.profile IS 'Stores user-specific profile information, extending auth.users.';

DO $$
BEGIN
    PERFORM public.add_audit_columns('public', 'profile');
END $$;


-- ----------------------------------------
-- Functions
-- ----------------------------------------

-- Get the role of the currently authenticated user
CREATE OR REPLACE FUNCTION public.get_my_role()
RETURNS public.role
LANGUAGE sql STABLE
AS $$
    SELECT p.role
    FROM public.profile p
    WHERE p.id = auth.uid();
$$;
COMMENT ON FUNCTION public.get_my_role() IS 'Retrieves the role of the currently authenticated user.';


-- Get the organization ID of the currently authenticated user
CREATE OR REPLACE FUNCTION public.get_my_organization_id()
RETURNS UUID
LANGUAGE plpgsql STABLE SECURITY DEFINER
AS $$
DECLARE
    user_id UUID := auth.uid();
    org_id UUID;
BEGIN
    -- First try to get organization_id from profile
    SELECT organization_id INTO org_id
    FROM public.profile
    WHERE id = user_id;

    -- If no profile exists, fall back to organization created by user
    -- Use SECURITY DEFINER to bypass RLS and avoid infinite recursion
    IF org_id IS NULL THEN
        SELECT id INTO org_id
        FROM public.organization
        WHERE created_by = user_id
        LIMIT 1;
    END IF;

    RETURN org_id;
END;
$$;
COMMENT ON FUNCTION public.get_my_organization_id() IS 'Retrieves the organization ID of the currently authenticated user.';


-- ----------------------------------------
-- RLS Policies
-- ----------------------------------------

-- Allow users to read their own profile
CREATE POLICY "Allow users to read own profile"
    ON public.profile
    FOR SELECT
    TO authenticated
    USING (id = auth.uid());

-- Allow users to create their own profile
CREATE POLICY "Allow users to create own profile"
    ON public.profile
    FOR INSERT
    TO authenticated
    WITH CHECK (id = auth.uid());

-- Allow users to update their own profile
CREATE POLICY "Allow users to update own profile"
    ON public.profile
    FOR UPDATE
    TO authenticated
    USING (id = auth.uid())
    WITH CHECK (id = auth.uid());


-- ----------------------------------------
-- Trigger Functions
-- ----------------------------------------

-- Trigger function to auto-assign highest role for organization creators
CREATE OR REPLACE FUNCTION public.auto_assign_highest_role_on_profile_insert()
RETURNS TRIGGER AS $$
DECLARE
    v_org_type public.organization_type;
    v_highest_role public.role;
    v_org_has_highest_role BOOLEAN;
BEGIN
    -- Only auto-assign role if no role is explicitly set
    IF NEW.role IS NOT NULL THEN
        RETURN NEW;
    END IF;

    -- Get organization type
    SELECT type INTO v_org_type
    FROM public.organization
    WHERE id = NEW.organization_id;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Organization not found for profile creation';
    END IF;

    -- Check if organization already has a user with the highest role
    v_org_has_highest_role := public.organization_has_highest_role_user(NEW.organization_id);

    -- If no highest role user exists, assign the highest role to this user
    IF NOT v_org_has_highest_role THEN
        v_highest_role := public.get_highest_role_for_organization_type(v_org_type);
        NEW.role := v_highest_role;
    ELSE
        -- If highest role user exists, assign a default lower role
        NEW.role := CASE
            WHEN v_org_type = 'consultancy' THEN 'consultancy_employee'::public.role
            WHEN v_org_type = 'tenant' THEN 'tenant_compliance'::public.role
            ELSE NULL
        END;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION public.auto_assign_highest_role_on_profile_insert()
IS 'Automatically assigns the highest role to the first user of an organization, or a default role to subsequent users.';


-- Trigger function to prevent removing the last highest role user
CREATE OR REPLACE FUNCTION public.prevent_last_highest_role_removal()
RETURNS TRIGGER AS $$
DECLARE
    v_org_type public.organization_type;
    v_highest_role public.role;
    v_count INTEGER;
BEGIN
    -- Get organization type
    SELECT type INTO v_org_type
    FROM public.organization
    WHERE id = COALESCE(OLD.organization_id, NEW.organization_id);

    IF NOT FOUND THEN
        RETURN COALESCE(NEW, OLD);
    END IF;

    v_highest_role := public.get_highest_role_for_organization_type(v_org_type);

    -- For DELETE operations
    IF TG_OP = 'DELETE' THEN
        -- Check if we're deleting a user with the highest role
        IF OLD.role = v_highest_role THEN
            -- Count remaining users with the highest role
            SELECT COUNT(*) INTO v_count
            FROM public.profile
            WHERE organization_id = OLD.organization_id
            AND role = v_highest_role
            AND id != OLD.id;

            -- Prevent deletion if this is the last highest role user
            IF v_count = 0 THEN
                RAISE EXCEPTION 'Cannot delete the last user with the highest role (%) in organization', v_highest_role;
            END IF;
        END IF;
        RETURN OLD;
    END IF;

    -- For UPDATE operations
    IF TG_OP = 'UPDATE' THEN
        -- Check if we're changing a user's role from the highest role
        IF OLD.role = v_highest_role AND NEW.role != v_highest_role THEN
            -- Count remaining users with the highest role (excluding this user)
            SELECT COUNT(*) INTO v_count
            FROM public.profile
            WHERE organization_id = OLD.organization_id
            AND role = v_highest_role
            AND id != OLD.id;

            -- Prevent role change if this is the last highest role user
            IF v_count = 0 THEN
                RAISE EXCEPTION 'Cannot change role of the last user with the highest role (%) in organization', v_highest_role;
            END IF;
        END IF;
        RETURN NEW;
    END IF;

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION public.prevent_last_highest_role_removal()
IS 'Prevents deletion or role change of the last user with the highest role in an organization.';


-- ----------------------------------------
-- Triggers
-- ----------------------------------------

-- Trigger to auto-assign highest role on profile creation
CREATE TRIGGER trigger_auto_assign_highest_role
    BEFORE INSERT ON public.profile
    FOR EACH ROW
    EXECUTE FUNCTION public.auto_assign_highest_role_on_profile_insert();

-- Trigger to prevent removing the last highest role user
CREATE TRIGGER trigger_prevent_last_highest_role_removal
    BEFORE UPDATE OR DELETE ON public.profile
    FOR EACH ROW
    EXECUTE FUNCTION public.prevent_last_highest_role_removal();


-- ----------------------------------------
-- Additional Constraints
-- ----------------------------------------

-- Now that triggers are in place, make role NOT NULL
ALTER TABLE public.profile ALTER COLUMN role SET NOT NULL;

-- Audit trigger is automatically created by add_audit_columns function
