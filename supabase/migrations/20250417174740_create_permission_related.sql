-- ----------------------------------------
-- Tables
-- ----------------------------------------

CREATE TABLE public.permission (
    name VARCHAR(50) PRIMARY KEY
);
ALTER TABLE public.permission ENABLE ROW LEVEL SECURITY;
COMMENT ON TABLE public.permission IS 'Defines available permissions.';


CREATE TABLE public.role_permission (
    role       public.role NOT NULL,
    permission VARCHAR(50) REFERENCES public.permission (name) ON DELETE RESTRICT,
    PRIMARY KEY (role, permission)
);
ALTER TABLE public.role_permission ENABLE ROW LEVEL SECURITY;
COMMENT ON TABLE public.role_permission IS 'Maps user roles to their assigned permissions';


-- ----------------------------------------
-- Functions
-- ----------------------------------------

CREATE OR REPLACE FUNCTION public.check_permission(p_permission VARCHAR(50))
RETURNS BOOLEAN
LANGUAGE sql STABLE
AS $$
SELECT EXISTS (
    SELECT 1
    FROM public.role_permission permission
    WHERE permission.role = public.get_my_role()
      AND permission.permission = p_permission
);
$$;
COMMENT ON FUNCTION public.check_permission(VARCHAR(50)) IS 'Checks if the currently authenticated user role has the specified permission.';


-- ----------------------------------------
-- RLS Policies
-- ----------------------------------------

CREATE POLICY "Allow users to read permissions"
    ON public.permission
    FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Allow users to read role permissions"
    ON public.role_permission
    FOR SELECT
    TO authenticated
    USING (true);
