-- ----------------------------------------
-- ENUMs
-- ----------------------------------------

CREATE TYPE public.gender AS ENUM (
    'male',
    'female',
    'other'
);


CREATE TYPE public.role AS ENUM (
    'consultancy_manager',
    'consultancy_employee',
    'tenant_manager',
    'tenant_compliance',
    'tenant_purchaser',
    'tenant_seller'
);


-- ----------------------------------------
-- Functions
-- ----------------------------------------

-- Trigger function for updating the updated_at column
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
RETURN NEW;
END;
$$ LANGUAGE plpgsql;


-- Trigger function for updating the updated_by column
CREATE OR REPLACE FUNCTION public.update_updated_by_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_by = auth.uid();
RETURN NEW;
END;
$$ LANGUAGE plpgsql;
