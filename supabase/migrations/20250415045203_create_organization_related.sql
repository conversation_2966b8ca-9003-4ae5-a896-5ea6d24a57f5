-- ----------------------------------------
-- ENUMs
-- ----------------------------------------

CREATE TYPE public.organization_type AS ENUM (
    'consultancy',
    'tenant'
);


-- ----------------------------------------
-- Tables
-- ----------------------------------------

CREATE TABLE public.organization (
    id                UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
    consultancy_organization_id UUID REFERENCES public.organization (id) ON DELETE SET NULL,
    name              TEXT NOT NULL,
    type              public.organization_type NOT NULL,
    line1             TEXT NOT NULL,
    line2             TEXT,
    postal_code       TEXT,
    city              TEXT NOT NULL,
    state             TEXT,
    country           CHAR(2) NOT NULL REFERENCES public.country(code)
    -- created_at        TIMESTAMPTZ NOT NULL DEFAULT now(),
    -- updated_at        TIMESTAMPTZ NOT NULL DEFAULT now(),
    -- created_by        UUID REFERENCES auth.users(id) NOT NULL,
    -- updated_by        UUID REFERENCES auth.users(id) NOT NULL
);
ALTER TABLE public.organization ENABLE ROW LEVEL SECURITY;
COMMENT ON TABLE public.organization IS 'Represents a Consultancy or Tenant (Customer).';

DO $$
BEGIN
    PERFORM public.add_audit_columns('public', 'organization');
END $$;


-- ----------------------------------------
-- RLS Policies
-- ----------------------------------------

-- See Migration 20250418061304_add_organization_rls_policies.sql


-- ----------------------------------------
-- Triggers
-- ----------------------------------------

-- CREATE TRIGGER update_organization_updated_at
--     BEFORE UPDATE ON public.organization
--     FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- CREATE TRIGGER update_organization_updated_by
--     BEFORE UPDATE ON public.organization
--     FOR EACH ROW EXECUTE FUNCTION public.update_updated_by_column();
