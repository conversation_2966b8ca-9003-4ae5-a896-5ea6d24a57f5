-- Trigger function to populate audit columns
CREATE OR REPLACE FUNCTION public.populate_audit_columns()
RETURNS TRIGGER AS $$
DECLARE
    current_user_id UUID := auth.uid();
BEGIN
    IF (TG_OP = 'INSERT') THEN
        -- Set created_at only if not already set by DEFAULT
        -- IF NEW.created_at IS NULL THEN
        --     NEW.created_at := current_timestamp;
        -- END IF;
        NEW.created_by := current_user_id;
        -- NEW.updated_at := current_timestamp; -- Set updated_at on insert as well
        NEW.updated_by := current_user_id;
    ELSIF (TG_OP = 'UPDATE') THEN
        -- Prevent modification of created_at and created_by on update
        IF NEW.created_at IS DISTINCT FROM OLD.created_at THEN
            NEW.created_at = OLD.created_at;
        END IF;
        IF NEW.created_by IS DISTINCT FROM OLD.created_by THEN
            NEW.created_by = OLD.created_by;
        END IF;

        NEW.updated_at := current_timestamp;
        NEW.updated_by := current_user_id;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE PLPGSQL;
COMMENT ON FUNCTION public.populate_audit_columns() IS 'Trigger function to automatically set created_at, created_by, updated_at, and updated_by columns on insert and update.';


-- Adds standard audit columns and the trigger to a table
CREATE OR REPLACE FUNCTION public.add_audit_columns(p_schema_name TEXT, p_table_name TEXT)
RETURNS VOID AS $$
BEGIN
    EXECUTE FORMAT('ALTER TABLE %I.%I ADD COLUMN IF NOT EXISTS created_at TIMESTAMPTZ NOT NULL DEFAULT now()', p_schema_name, p_table_name);
    EXECUTE FORMAT('ALTER TABLE %I.%I ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES auth.users(id) NOT NULL', p_schema_name, p_table_name);
    EXECUTE FORMAT('ALTER TABLE %I.%I ADD COLUMN IF NOT EXISTS updated_at TIMESTAMPTZ NOT NULL DEFAULT now()', p_schema_name, p_table_name);
    EXECUTE FORMAT('ALTER TABLE %I.%I ADD COLUMN IF NOT EXISTS updated_by UUID REFERENCES auth.users(id) NOT NULL', p_schema_name, p_table_name);

    EXECUTE FORMAT(
            'CREATE OR REPLACE TRIGGER %I
            BEFORE INSERT OR UPDATE ON %I.%I
            FOR EACH ROW EXECUTE FUNCTION public.populate_audit_columns()',
            'audit_' || p_table_name, p_schema_name, p_table_name
    );
END;
$$ LANGUAGE PLPGSQL;
COMMENT ON FUNCTION public.add_audit_columns(TEXT, TEXT) IS 'Adds standard audit columns (created_at, created_by, updated_at, updated_by) and creates the audit trigger for the specified table.';
