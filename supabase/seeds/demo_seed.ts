import {
    createSeedClient,
    type SeedClient,
    type genderEnum,
    type roleEnum,
    type organization_typeEnum,
} from '@snaplet/seed';
import {fakerDE as faker} from '@faker-js/faker';

// --- Configuration Constants ---
const NUM_CONSULTANCIES = 2;
const NUM_TENANT_ORGS_PER_CONSULTANCY = 3;
const NUM_USERS_PER_TENANT = 5;
const INSTANCE_ID = '00000000-0000-0000-0000-000000000000';

// --- Helper Types ---
interface UserWithIdentityResult {
    users: Record<string, any>[];
    identities: Record<string, any>[];
}

// --- Reusable Data Generation Functions ---

/**
 * Generates common user data fields for Supabase Auth.
 * @param email - The user's email.
 * @param currentTime - The timestamp for creation/confirmation.
 * @returns Base user data object.
 */
const generateBaseUserData = (email: string, currentTime: Date) => ({
    instance_id: INSTANCE_ID,
    aud: 'authenticated' as const,
    role: 'authenticated' as const,
    email: email,
    encrypted_password: '',
    email_confirmed_at: currentTime,
    invited_at: null,
    confirmation_token: null,
    confirmation_sent_at: currentTime,
    recovery_token: null,
    recovery_sent_at: null,
    email_change: '',
    email_change_sent_at: null,
    last_sign_in_at: null,
    raw_app_meta_data: {provider: 'email', providers: ['email']},
    raw_user_meta_data: {email_verified: true},
    is_super_admin: null,
    created_at: currentTime,
    updated_at: currentTime,
    phone: null,
    phone_confirmed_at: null,
    phone_change: '',
    phone_change_token: '',
    phone_change_sent_at: null,
    email_change_token_current: null,
    email_change_token_new: null,
    email_change_confirm_status: 0,
    banned_until: null,
    reauthentication_token: null,
    reauthentication_sent_at: null,
    is_sso_user: false,
    deleted_at: null,
    is_anonymous: false,
});

/**
 * Generates German-specific organization address data using Faker defaults.
 * @returns Address data object.
 */
const generateGermanOrganizationAddress = () => {
    return {
        line1: faker.location.streetAddress(),
        line2: faker.datatype.boolean(0.2) ? faker.location.secondaryAddress() : null,
        city: faker.location.city(),
        state: faker.location.state(),
        country: faker.helpers.arrayElement(['DE', 'DE', 'DE', 'AT', 'CH', 'NL', 'FR']),
        postal_code: faker.location.zipCode('#####'),
    };
};

// --- Reusable Seeding Functions ---

/**
 * Creates a user and their corresponding identity using nested factories.
 * Uses the ctx.data pattern to access user_id within the nested factory.
 * @param seed - The Snaplet seed client instance.
 * @param email - The user's email.
 * @param currentTime - The timestamp for creation.
 * @returns The created user object.
 * @throws Error if user creation fails.
 */
async function createUserWithIdentity(
    seed: SeedClient,
    email: string,
    currentTime: Date,
): Promise<Record<string, any>> {
    const baseUserData = generateBaseUserData(email, currentTime);

    const result = await seed.users(userFactory => userFactory(1, {
        ...baseUserData,
        identities: identityFactory => identityFactory(1, {
            provider_id: (ctx) => {
                // @ts-ignore - Relying on runtime behavior of ctx.data for user_id
                return ctx.data?.user_id || '';
            },
            identity_data: (ctx) => {
                // @ts-ignore - Relying on runtime behavior of ctx.data for user_id
                const userId = ctx.data?.user_id || '';
                if (!userId) {
                    console.warn(`[createUserWithIdentity] Warning: Could not get user_id via ctx.data for identity_data of ${email}. 'sub' might be empty.`);
                }
                return {
                    sub: userId,
                    email: email,
                    email_verified: true,
                    phone_verified: false,
                };
            },
            provider: 'email',
            last_sign_in_at: currentTime,
            created_at: currentTime,
            updated_at: currentTime,
            // user_id: Foreign key linked automatically by Snaplet nesting
        }),
    })) as UserWithIdentityResult;

    const user = result?.users?.[0];
    if (!user?.id) {
        console.error('[createUserWithIdentity] User creation failed. Result:', JSON.stringify(result, null, 2));
        throw new Error(`Failed to create user with email ${email}`);
    }

    const identity = result?.identities?.[0];
    if (!identity?.id) {
        console.warn(`[createUserWithIdentity] Warning: Identity data might not have been returned in the result for user ${user.id}. Verify identity creation in the database.`);
    }

    return user;
}


/**
 * Creates an organization record using German locale data.
 * @param seed - The Snaplet seed client instance.
 * @param name - Organization name.
 * @param type - Organization type ('consultancy' or 'tenant').
 * @param createdById - UUID of the user creating the organization.
 * @param currentTime - Timestamp for creation.
 * @param consultancyOrgId - Optional UUID of the parent consultancy.
 * @returns The created organization object.
 * @throws Error if organization creation fails.
 */
async function createOrganization(
    seed: SeedClient,
    name: string,
    type: organization_typeEnum,
    createdById: string,
    currentTime: Date,
    consultancyOrgId: string | null = null,
): Promise<Record<string, any>> {
    const address = generateGermanOrganizationAddress();

    const orgData = {
        name: name,
        type: type,
        ...address,
        consultancy_organization_id: consultancyOrgId,
        created_by: createdById,
        updated_by: createdById,
        created_at: currentTime,
        updated_at: currentTime,
    };

    const result = await seed.organization(x => x(1, orgData));
    const organization = result?.organization?.[0];

    if (!organization?.id) {
        console.error(`[createOrganization] Organization creation failed for ${name}. Result:`, JSON.stringify(result, null, 2));
        throw new Error(`Failed to create organization: ${name}`);
    }
    return organization;
}

/**
 * Creates a user profile record using German locale data.
 * @param seed - The Snaplet seed client instance.
 * @param userId - UUID of the user the profile belongs to.
 * @param orgId - UUID of the organization the user belongs to.
 * @param role - User's role within the organization.
 * @param firstName - User's first name.
 * @param lastName - User's last name.
 * @param createdById - UUID of the user creating the profile.
 * @param currentTime - Timestamp for creation.
 * @param onboardingComplete - Status of onboarding.
 * @returns The created profile object.
 * @throws Error if profile creation fails.
 */
async function createProfile(
    seed: SeedClient,
    userId: string,
    orgId: string,
    role: roleEnum,
    firstName: string,
    lastName: string,
    createdById: string,
    currentTime: Date,
): Promise<Record<string, any>> {
    const profileData = {
        id: userId,
        organization_id: orgId,
        role: role,
        first_name: firstName,
        last_name: lastName,
        gender: faker.helpers.arrayElement(['male', 'female', 'other'] as genderEnum[]),
        phone_number: faker.phone.number({style: 'international'}),
        created_at: currentTime,
        updated_at: currentTime,
        created_by: createdById,
        updated_by: createdById,
    };

    const result = await seed.profile(x => x(1, profileData));
    const profile = result?.profile?.[0];

    if (!profile?.id) {
        console.error(`[createProfile] Profile creation failed for user ${userId}. Result:`, JSON.stringify(result, null, 2));
        throw new Error(`Failed to create profile for user: ${userId}`);
    }
    return profile;
}


// --- Main Seeding Logic ---
const main = async () => {
    // List of tables with audit triggers starting with "audit_"
    const auditTriggerTables = ['organization', 'profile'];
    let seed: SeedClient | undefined;

    try {
        seed = await createSeedClient({
            dryRun: true, // To generate SQL instead of executing
        });

        const currentTime = new Date();

        for (const tableName of auditTriggerTables) {
            console.log(`ALTER TABLE public.${tableName} DISABLE TRIGGER audit_${tableName};`);
        }

        for (let cIdx = 0; cIdx < NUM_CONSULTANCIES; cIdx++) {
            const consultancyIndex = cIdx + 1;

            const managerFirstName = faker.person.firstName();
            const managerLastName = faker.person.lastName();
            // Sanitize last name for email (important for names with special chars)
            const managerEmail = `consultancy${consultancyIndex}.manager.${managerLastName.toLowerCase().replace(/[^a-z0-9]/g, '')}@example.com`;

            const managerUser = await createUserWithIdentity(seed, managerEmail, currentTime);

            const consultancyName = `${faker.company.name()} ${consultancyIndex}`;
            const consultancyOrg = await createOrganization(
                seed,
                consultancyName,
                'consultancy',
                managerUser.id,
                currentTime,
            );

            await createProfile(
                seed,
                managerUser.id,
                consultancyOrg.id,
                'consultancy_manager',
                managerFirstName,
                managerLastName,
                managerUser.id,
                currentTime,
            );

            for (let tIdx = 0; tIdx < NUM_TENANT_ORGS_PER_CONSULTANCY; tIdx++) {
                const tenantIndex = tIdx + 1;

                const tenantName = `${faker.company.name()} C${consultancyIndex}T${tenantIndex}`;
                const tenantOrg = await createOrganization(
                    seed,
                    tenantName,
                    'tenant',
                    managerUser.id,
                    currentTime,
                    consultancyOrg.id,
                );

                const managerFirstName = faker.person.firstName();
                const managerLastName = faker.person.lastName();
                const managerEmail = `tenant${consultancyIndex}-${tenantIndex}.manager.${managerLastName.toLowerCase().replace(/[^a-z0-9]/g, '')}@example.com`;

                const tenantManagerUser = await createUserWithIdentity(seed, managerEmail, currentTime);

                await createProfile(
                    seed,
                    tenantManagerUser.id,
                    tenantOrg.id,
                    'tenant_manager',
                    managerFirstName,
                    managerLastName,
                    managerUser.id, // Consultancy manager creates tenant manager profile
                    currentTime,
                );

                const tenantRoles: roleEnum[] = ['tenant_compliance', 'tenant_purchaser', 'tenant_seller'];
                for (let uIdx = 0; uIdx < NUM_USERS_PER_TENANT; uIdx++) {
                    const userIndex = uIdx + 1;
                    const userFirstName = faker.person.firstName();
                    const userLastName = faker.person.lastName();
                    const userEmail = `tenant${consultancyIndex}-${tenantIndex}.user${userIndex}.${userLastName.toLowerCase().replace(/[^a-z0-9]/g, '')}@example.com`;

                    const tenantUser = await createUserWithIdentity(seed, userEmail, currentTime);

                    const userRole = faker.helpers.arrayElement(tenantRoles);
                    await createProfile(
                        seed,
                        tenantUser.id,
                        tenantOrg.id,
                        userRole,
                        userFirstName,
                        userLastName,
                        tenantManagerUser.id, // Tenant manager creates regular user profiles
                        currentTime,
                    );
                }
            }
        }

        // Set tokens to empty for OTP to work
        console.log(`UPDATE AUTH.USERS SET confirmation_token = '', recovery_token = '', email_change_token_current = '', email_change_token_new = '', reauthentication_token = '';`);

        for (const tableName of auditTriggerTables) {
            console.log(`ALTER TABLE public.${tableName} ENABLE TRIGGER audit_${tableName};`);
        }
    } catch (error) {
        console.error('\n--- ERROR DURING SEEDING PROCESS ---');
        if (error instanceof Error) {
            console.error('Error Message:', error.message);
            console.error('Stack Trace:', error.stack);
        } else {
            console.error('An unknown error occurred:', error);
        }
        process.exit(1);
    } finally {
        process.exit(0);
    }
};


main();
