ALTER TABLE public.organization DISABLE TRIGGER audit_organization;
ALTER TABLE public.profile DISABLE TRIGGER audit_profile;
INSERT INTO auth.users (instance_id,id,aud,role,email,encrypted_password,email_confirmed_at,invited_at,confirmation_token,confirmation_sent_at,recovery_token,recovery_sent_at,email_change_token_new,email_change,email_change_sent_at,last_sign_in_at,raw_app_meta_data,raw_user_meta_data,is_super_admin,created_at,updated_at,phone,phone_confirmed_at,phone_change,phone_change_token,phone_change_sent_at,email_change_token_current,email_change_confirm_status,banned_until,reauthentication_token,reauthentication_sent_at,is_sso_user,deleted_at,is_anonymous) VALUES ('00000000-0000-0000-0000-000000000000', '0f5d546a-dd80-5406-a005-a4f3061b9fb4', 'authenticated', 'authenticated', '<EMAIL>', '', '2025-07-18T13:29:46.249Z', NULL, NULL, '2025-07-18T13:29:46.249Z', NULL, NULL, NULL, '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"email_verified":true}', NULL, '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', NULL, NULL, '', '', NULL, NULL, 0, NULL, NULL, NULL, 'f', NULL, 'f');
INSERT INTO auth.identities (provider_id,user_id,identity_data,provider,last_sign_in_at,created_at,updated_at,id) VALUES ('0f5d546a-dd80-5406-a005-a4f3061b9fb4', '0f5d546a-dd80-5406-a005-a4f3061b9fb4', '{"sub":"0f5d546a-dd80-5406-a005-a4f3061b9fb4","email":"<EMAIL>","email_verified":true,"phone_verified":false}', 'email', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', 'eb2b4a57-df1b-56a8-8a95-ca16989907f9');
INSERT INTO public.organization (id,consultancy_organization_id,name,type,line1,line2,postal_code,city,state,country,created_at,created_by,updated_at,updated_by) VALUES ('c5bc6e6e-72ee-5b1a-9e63-65bf1e453498', NULL, 'Sischka UG 1', 'consultancy', 'Elsbachstr. 3', NULL, '22535', 'Celiadorf', 'Niedersachsen', 'DE', '2025-07-18T13:29:46.249Z', '0f5d546a-dd80-5406-a005-a4f3061b9fb4', '2025-07-18T13:29:46.249Z', '0f5d546a-dd80-5406-a005-a4f3061b9fb4');
INSERT INTO public.profile (id,first_name,last_name,gender,phone_number,organization_id,role,created_at,created_by,updated_at,updated_by) VALUES ('0f5d546a-dd80-5406-a005-a4f3061b9fb4', 'Bruno', 'Suffa', 'other', '+4914277941813', 'c5bc6e6e-72ee-5b1a-9e63-65bf1e453498', 'consultancy_manager', '2025-07-18T13:29:46.249Z', '0f5d546a-dd80-5406-a005-a4f3061b9fb4', '2025-07-18T13:29:46.249Z', '0f5d546a-dd80-5406-a005-a4f3061b9fb4');
INSERT INTO public.organization (id,consultancy_organization_id,name,type,line1,line2,postal_code,city,state,country,created_at,created_by,updated_at,updated_by) VALUES ('5d028b9f-5933-5af3-a440-d93103aa0a79', NULL, 'Drees-Kuschmann C1T1', 'tenant', 'Friedrich-Ebert-Platz 36a', NULL, '11645', 'West Eleonorascheid', 'Berlin', 'DE', '2025-07-18T13:29:46.249Z', '0f5d546a-dd80-5406-a005-a4f3061b9fb4', '2025-07-18T13:29:46.249Z', '0f5d546a-dd80-5406-a005-a4f3061b9fb4');
UPDATE public.organization SET consultancy_organization_id = 'c5bc6e6e-72ee-5b1a-9e63-65bf1e453498' WHERE id = '5d028b9f-5933-5af3-a440-d93103aa0a79';
INSERT INTO auth.users (instance_id,id,aud,role,email,encrypted_password,email_confirmed_at,invited_at,confirmation_token,confirmation_sent_at,recovery_token,recovery_sent_at,email_change_token_new,email_change,email_change_sent_at,last_sign_in_at,raw_app_meta_data,raw_user_meta_data,is_super_admin,created_at,updated_at,phone,phone_confirmed_at,phone_change,phone_change_token,phone_change_sent_at,email_change_token_current,email_change_confirm_status,banned_until,reauthentication_token,reauthentication_sent_at,is_sso_user,deleted_at,is_anonymous) VALUES ('00000000-0000-0000-0000-000000000000', '201a57ad-3e5f-593b-903c-61b025ec68b0', 'authenticated', 'authenticated', '<EMAIL>', '', '2025-07-18T13:29:46.249Z', NULL, NULL, '2025-07-18T13:29:46.249Z', NULL, NULL, NULL, '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"email_verified":true}', NULL, '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', NULL, NULL, '', '', NULL, NULL, 0, NULL, NULL, NULL, 'f', NULL, 'f');
INSERT INTO auth.identities (provider_id,user_id,identity_data,provider,last_sign_in_at,created_at,updated_at,id) VALUES ('201a57ad-3e5f-593b-903c-61b025ec68b0', '201a57ad-3e5f-593b-903c-61b025ec68b0', '{"sub":"201a57ad-3e5f-593b-903c-61b025ec68b0","email":"<EMAIL>","email_verified":true,"phone_verified":false}', 'email', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '57464131-3f56-51fc-87d3-2899148cffaa');
INSERT INTO public.profile (id,first_name,last_name,gender,phone_number,organization_id,role,created_at,created_by,updated_at,updated_by) VALUES ('201a57ad-3e5f-593b-903c-61b025ec68b0', 'Marlena', 'Losch', 'male', '+496596010707', '5d028b9f-5933-5af3-a440-d93103aa0a79', 'tenant_manager', '2025-07-18T13:29:46.249Z', '0f5d546a-dd80-5406-a005-a4f3061b9fb4', '2025-07-18T13:29:46.249Z', '0f5d546a-dd80-5406-a005-a4f3061b9fb4');
INSERT INTO auth.users (instance_id,id,aud,role,email,encrypted_password,email_confirmed_at,invited_at,confirmation_token,confirmation_sent_at,recovery_token,recovery_sent_at,email_change_token_new,email_change,email_change_sent_at,last_sign_in_at,raw_app_meta_data,raw_user_meta_data,is_super_admin,created_at,updated_at,phone,phone_confirmed_at,phone_change,phone_change_token,phone_change_sent_at,email_change_token_current,email_change_confirm_status,banned_until,reauthentication_token,reauthentication_sent_at,is_sso_user,deleted_at,is_anonymous) VALUES ('00000000-0000-0000-0000-000000000000', 'd68cbd6a-14a1-512a-8968-78c2c7d64982', 'authenticated', 'authenticated', '<EMAIL>', '', '2025-07-18T13:29:46.249Z', NULL, NULL, '2025-07-18T13:29:46.249Z', NULL, NULL, NULL, '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"email_verified":true}', NULL, '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', NULL, NULL, '', '', NULL, NULL, 0, NULL, NULL, NULL, 'f', NULL, 'f');
INSERT INTO auth.identities (provider_id,user_id,identity_data,provider,last_sign_in_at,created_at,updated_at,id) VALUES ('d68cbd6a-14a1-512a-8968-78c2c7d64982', 'd68cbd6a-14a1-512a-8968-78c2c7d64982', '{"sub":"d68cbd6a-14a1-512a-8968-78c2c7d64982","email":"<EMAIL>","email_verified":true,"phone_verified":false}', 'email', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', 'a0833a15-09e5-502d-acd5-307db564869a');
INSERT INTO public.profile (id,first_name,last_name,gender,phone_number,organization_id,role,created_at,created_by,updated_at,updated_by) VALUES ('d68cbd6a-14a1-512a-8968-78c2c7d64982', 'Annabel', 'Ranftl', 'female', '+492181483847', '5d028b9f-5933-5af3-a440-d93103aa0a79', 'tenant_purchaser', '2025-07-18T13:29:46.249Z', '201a57ad-3e5f-593b-903c-61b025ec68b0', '2025-07-18T13:29:46.249Z', '201a57ad-3e5f-593b-903c-61b025ec68b0');
INSERT INTO auth.users (instance_id,id,aud,role,email,encrypted_password,email_confirmed_at,invited_at,confirmation_token,confirmation_sent_at,recovery_token,recovery_sent_at,email_change_token_new,email_change,email_change_sent_at,last_sign_in_at,raw_app_meta_data,raw_user_meta_data,is_super_admin,created_at,updated_at,phone,phone_confirmed_at,phone_change,phone_change_token,phone_change_sent_at,email_change_token_current,email_change_confirm_status,banned_until,reauthentication_token,reauthentication_sent_at,is_sso_user,deleted_at,is_anonymous) VALUES ('00000000-0000-0000-0000-000000000000', '6761c84d-4721-514a-925a-e32a02b7a92d', 'authenticated', 'authenticated', '<EMAIL>', '', '2025-07-18T13:29:46.249Z', NULL, NULL, '2025-07-18T13:29:46.249Z', NULL, NULL, NULL, '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"email_verified":true}', NULL, '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', NULL, NULL, '', '', NULL, NULL, 0, NULL, NULL, NULL, 'f', NULL, 'f');
INSERT INTO auth.identities (provider_id,user_id,identity_data,provider,last_sign_in_at,created_at,updated_at,id) VALUES ('6761c84d-4721-514a-925a-e32a02b7a92d', '6761c84d-4721-514a-925a-e32a02b7a92d', '{"sub":"6761c84d-4721-514a-925a-e32a02b7a92d","email":"<EMAIL>","email_verified":true,"phone_verified":false}', 'email', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '1d01abe3-e669-5460-8f39-f4463e357583');
INSERT INTO public.profile (id,first_name,last_name,gender,phone_number,organization_id,role,created_at,created_by,updated_at,updated_by) VALUES ('6761c84d-4721-514a-925a-e32a02b7a92d', 'Henri', 'Stief', 'male', '+499897442292', '5d028b9f-5933-5af3-a440-d93103aa0a79', 'tenant_seller', '2025-07-18T13:29:46.249Z', '201a57ad-3e5f-593b-903c-61b025ec68b0', '2025-07-18T13:29:46.249Z', '201a57ad-3e5f-593b-903c-61b025ec68b0');
INSERT INTO auth.users (instance_id,id,aud,role,email,encrypted_password,email_confirmed_at,invited_at,confirmation_token,confirmation_sent_at,recovery_token,recovery_sent_at,email_change_token_new,email_change,email_change_sent_at,last_sign_in_at,raw_app_meta_data,raw_user_meta_data,is_super_admin,created_at,updated_at,phone,phone_confirmed_at,phone_change,phone_change_token,phone_change_sent_at,email_change_token_current,email_change_confirm_status,banned_until,reauthentication_token,reauthentication_sent_at,is_sso_user,deleted_at,is_anonymous) VALUES ('00000000-0000-0000-0000-000000000000', '9deced8e-4a1c-5312-a45f-745cbea14010', 'authenticated', 'authenticated', '<EMAIL>', '', '2025-07-18T13:29:46.249Z', NULL, NULL, '2025-07-18T13:29:46.249Z', NULL, NULL, NULL, '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"email_verified":true}', NULL, '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', NULL, NULL, '', '', NULL, NULL, 0, NULL, NULL, NULL, 'f', NULL, 'f');
INSERT INTO auth.identities (provider_id,user_id,identity_data,provider,last_sign_in_at,created_at,updated_at,id) VALUES ('9deced8e-4a1c-5312-a45f-745cbea14010', '9deced8e-4a1c-5312-a45f-745cbea14010', '{"sub":"9deced8e-4a1c-5312-a45f-745cbea14010","email":"<EMAIL>","email_verified":true,"phone_verified":false}', 'email', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '6d97b892-e61a-54f9-a697-33cadf871cd0');
INSERT INTO public.profile (id,first_name,last_name,gender,phone_number,organization_id,role,created_at,created_by,updated_at,updated_by) VALUES ('9deced8e-4a1c-5312-a45f-745cbea14010', 'Wibke', 'Balck', 'female', '+4965910832311', '5d028b9f-5933-5af3-a440-d93103aa0a79', 'tenant_purchaser', '2025-07-18T13:29:46.249Z', '201a57ad-3e5f-593b-903c-61b025ec68b0', '2025-07-18T13:29:46.249Z', '201a57ad-3e5f-593b-903c-61b025ec68b0');
INSERT INTO auth.users (instance_id,id,aud,role,email,encrypted_password,email_confirmed_at,invited_at,confirmation_token,confirmation_sent_at,recovery_token,recovery_sent_at,email_change_token_new,email_change,email_change_sent_at,last_sign_in_at,raw_app_meta_data,raw_user_meta_data,is_super_admin,created_at,updated_at,phone,phone_confirmed_at,phone_change,phone_change_token,phone_change_sent_at,email_change_token_current,email_change_confirm_status,banned_until,reauthentication_token,reauthentication_sent_at,is_sso_user,deleted_at,is_anonymous) VALUES ('00000000-0000-0000-0000-000000000000', 'eac96073-4387-5a58-a310-bcef8dcafab8', 'authenticated', 'authenticated', '<EMAIL>', '', '2025-07-18T13:29:46.249Z', NULL, NULL, '2025-07-18T13:29:46.249Z', NULL, NULL, NULL, '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"email_verified":true}', NULL, '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', NULL, NULL, '', '', NULL, NULL, 0, NULL, NULL, NULL, 'f', NULL, 'f');
INSERT INTO auth.identities (provider_id,user_id,identity_data,provider,last_sign_in_at,created_at,updated_at,id) VALUES ('eac96073-4387-5a58-a310-bcef8dcafab8', 'eac96073-4387-5a58-a310-bcef8dcafab8', '{"sub":"eac96073-4387-5a58-a310-bcef8dcafab8","email":"<EMAIL>","email_verified":true,"phone_verified":false}', 'email', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', 'dfac81ac-b2dc-5418-8303-f09e2086ee5c');
INSERT INTO public.profile (id,first_name,last_name,gender,phone_number,organization_id,role,created_at,created_by,updated_at,updated_by) VALUES ('eac96073-4387-5a58-a310-bcef8dcafab8', 'Tamina', 'Sinnhuber', 'male', '+49601814275149', '5d028b9f-5933-5af3-a440-d93103aa0a79', 'tenant_seller', '2025-07-18T13:29:46.249Z', '201a57ad-3e5f-593b-903c-61b025ec68b0', '2025-07-18T13:29:46.249Z', '201a57ad-3e5f-593b-903c-61b025ec68b0');
INSERT INTO auth.users (instance_id,id,aud,role,email,encrypted_password,email_confirmed_at,invited_at,confirmation_token,confirmation_sent_at,recovery_token,recovery_sent_at,email_change_token_new,email_change,email_change_sent_at,last_sign_in_at,raw_app_meta_data,raw_user_meta_data,is_super_admin,created_at,updated_at,phone,phone_confirmed_at,phone_change,phone_change_token,phone_change_sent_at,email_change_token_current,email_change_confirm_status,banned_until,reauthentication_token,reauthentication_sent_at,is_sso_user,deleted_at,is_anonymous) VALUES ('00000000-0000-0000-0000-000000000000', 'c0af2100-52a8-5c33-9839-a678c94f1df0', 'authenticated', 'authenticated', '<EMAIL>', '', '2025-07-18T13:29:46.249Z', NULL, NULL, '2025-07-18T13:29:46.249Z', NULL, NULL, NULL, '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"email_verified":true}', NULL, '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', NULL, NULL, '', '', NULL, NULL, 0, NULL, NULL, NULL, 'f', NULL, 'f');
INSERT INTO auth.identities (provider_id,user_id,identity_data,provider,last_sign_in_at,created_at,updated_at,id) VALUES ('c0af2100-52a8-5c33-9839-a678c94f1df0', 'c0af2100-52a8-5c33-9839-a678c94f1df0', '{"sub":"c0af2100-52a8-5c33-9839-a678c94f1df0","email":"<EMAIL>","email_verified":true,"phone_verified":false}', 'email', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', 'd2934e50-6720-5cc4-8714-b8e5413b9bb9');
INSERT INTO public.profile (id,first_name,last_name,gender,phone_number,organization_id,role,created_at,created_by,updated_at,updated_by) VALUES ('c0af2100-52a8-5c33-9839-a678c94f1df0', 'Mariella', 'Neumann', 'female', '+49760284715119', '5d028b9f-5933-5af3-a440-d93103aa0a79', 'tenant_seller', '2025-07-18T13:29:46.249Z', '201a57ad-3e5f-593b-903c-61b025ec68b0', '2025-07-18T13:29:46.249Z', '201a57ad-3e5f-593b-903c-61b025ec68b0');
INSERT INTO public.organization (id,consultancy_organization_id,name,type,line1,line2,postal_code,city,state,country,created_at,created_by,updated_at,updated_by) VALUES ('ba6ab718-5c91-5c6e-a145-8697208af538', NULL, 'Hackelbusch, Krull und Hooss C1T2', 'tenant', 'Von-Eichendorff-Str. 93c', NULL, '17918', 'Süd Enno', 'Niedersachsen', 'AT', '2025-07-18T13:29:46.249Z', '0f5d546a-dd80-5406-a005-a4f3061b9fb4', '2025-07-18T13:29:46.249Z', '0f5d546a-dd80-5406-a005-a4f3061b9fb4');
UPDATE public.organization SET consultancy_organization_id = 'c5bc6e6e-72ee-5b1a-9e63-65bf1e453498' WHERE id = 'ba6ab718-5c91-5c6e-a145-8697208af538';
INSERT INTO auth.users (instance_id,id,aud,role,email,encrypted_password,email_confirmed_at,invited_at,confirmation_token,confirmation_sent_at,recovery_token,recovery_sent_at,email_change_token_new,email_change,email_change_sent_at,last_sign_in_at,raw_app_meta_data,raw_user_meta_data,is_super_admin,created_at,updated_at,phone,phone_confirmed_at,phone_change,phone_change_token,phone_change_sent_at,email_change_token_current,email_change_confirm_status,banned_until,reauthentication_token,reauthentication_sent_at,is_sso_user,deleted_at,is_anonymous) VALUES ('00000000-0000-0000-0000-000000000000', '3c87848e-3dee-520f-9edf-8736cdd05f8f', 'authenticated', 'authenticated', '<EMAIL>', '', '2025-07-18T13:29:46.249Z', NULL, NULL, '2025-07-18T13:29:46.249Z', NULL, NULL, NULL, '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"email_verified":true}', NULL, '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', NULL, NULL, '', '', NULL, NULL, 0, NULL, NULL, NULL, 'f', NULL, 'f');
INSERT INTO auth.identities (provider_id,user_id,identity_data,provider,last_sign_in_at,created_at,updated_at,id) VALUES ('3c87848e-3dee-520f-9edf-8736cdd05f8f', '3c87848e-3dee-520f-9edf-8736cdd05f8f', '{"sub":"3c87848e-3dee-520f-9edf-8736cdd05f8f","email":"<EMAIL>","email_verified":true,"phone_verified":false}', 'email', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', 'aab2974f-7346-5760-a38b-b041e0b67ec0');
INSERT INTO public.profile (id,first_name,last_name,gender,phone_number,organization_id,role,created_at,created_by,updated_at,updated_by) VALUES ('3c87848e-3dee-520f-9edf-8736cdd05f8f', 'Alice', 'Möhsner', 'male', '+494356874550', 'ba6ab718-5c91-5c6e-a145-8697208af538', 'tenant_manager', '2025-07-18T13:29:46.249Z', '0f5d546a-dd80-5406-a005-a4f3061b9fb4', '2025-07-18T13:29:46.249Z', '0f5d546a-dd80-5406-a005-a4f3061b9fb4');
INSERT INTO auth.users (instance_id,id,aud,role,email,encrypted_password,email_confirmed_at,invited_at,confirmation_token,confirmation_sent_at,recovery_token,recovery_sent_at,email_change_token_new,email_change,email_change_sent_at,last_sign_in_at,raw_app_meta_data,raw_user_meta_data,is_super_admin,created_at,updated_at,phone,phone_confirmed_at,phone_change,phone_change_token,phone_change_sent_at,email_change_token_current,email_change_confirm_status,banned_until,reauthentication_token,reauthentication_sent_at,is_sso_user,deleted_at,is_anonymous) VALUES ('00000000-0000-0000-0000-000000000000', '8dddb152-b8aa-54cb-9b1a-3e9f9ecd9460', 'authenticated', 'authenticated', '<EMAIL>', '', '2025-07-18T13:29:46.249Z', NULL, NULL, '2025-07-18T13:29:46.249Z', NULL, NULL, NULL, '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"email_verified":true}', NULL, '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', NULL, NULL, '', '', NULL, NULL, 0, NULL, NULL, NULL, 'f', NULL, 'f');
INSERT INTO auth.identities (provider_id,user_id,identity_data,provider,last_sign_in_at,created_at,updated_at,id) VALUES ('8dddb152-b8aa-54cb-9b1a-3e9f9ecd9460', '8dddb152-b8aa-54cb-9b1a-3e9f9ecd9460', '{"sub":"8dddb152-b8aa-54cb-9b1a-3e9f9ecd9460","email":"<EMAIL>","email_verified":true,"phone_verified":false}', 'email', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', 'f0e5c17a-cb19-5673-aff9-f686b7d043b2');
INSERT INTO public.profile (id,first_name,last_name,gender,phone_number,organization_id,role,created_at,created_by,updated_at,updated_by) VALUES ('8dddb152-b8aa-54cb-9b1a-3e9f9ecd9460', 'Emely', 'Mertens', 'female', '+49622496322069', 'ba6ab718-5c91-5c6e-a145-8697208af538', 'tenant_purchaser', '2025-07-18T13:29:46.249Z', '3c87848e-3dee-520f-9edf-8736cdd05f8f', '2025-07-18T13:29:46.249Z', '3c87848e-3dee-520f-9edf-8736cdd05f8f');
INSERT INTO auth.users (instance_id,id,aud,role,email,encrypted_password,email_confirmed_at,invited_at,confirmation_token,confirmation_sent_at,recovery_token,recovery_sent_at,email_change_token_new,email_change,email_change_sent_at,last_sign_in_at,raw_app_meta_data,raw_user_meta_data,is_super_admin,created_at,updated_at,phone,phone_confirmed_at,phone_change,phone_change_token,phone_change_sent_at,email_change_token_current,email_change_confirm_status,banned_until,reauthentication_token,reauthentication_sent_at,is_sso_user,deleted_at,is_anonymous) VALUES ('00000000-0000-0000-0000-000000000000', '926c04ff-8506-5b92-b562-adb241165192', 'authenticated', 'authenticated', '<EMAIL>', '', '2025-07-18T13:29:46.249Z', NULL, NULL, '2025-07-18T13:29:46.249Z', NULL, NULL, NULL, '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"email_verified":true}', NULL, '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', NULL, NULL, '', '', NULL, NULL, 0, NULL, NULL, NULL, 'f', NULL, 'f');
INSERT INTO auth.identities (provider_id,user_id,identity_data,provider,last_sign_in_at,created_at,updated_at,id) VALUES ('926c04ff-8506-5b92-b562-adb241165192', '926c04ff-8506-5b92-b562-adb241165192', '{"sub":"926c04ff-8506-5b92-b562-adb241165192","email":"<EMAIL>","email_verified":true,"phone_verified":false}', 'email', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '9f374002-8660-5b4b-9360-7cae1581aa1f');
INSERT INTO public.profile (id,first_name,last_name,gender,phone_number,organization_id,role,created_at,created_by,updated_at,updated_by) VALUES ('926c04ff-8506-5b92-b562-adb241165192', 'Til', 'Deckert', 'other', '+491401036768', 'ba6ab718-5c91-5c6e-a145-8697208af538', 'tenant_seller', '2025-07-18T13:29:46.249Z', '3c87848e-3dee-520f-9edf-8736cdd05f8f', '2025-07-18T13:29:46.249Z', '3c87848e-3dee-520f-9edf-8736cdd05f8f');
INSERT INTO auth.users (instance_id,id,aud,role,email,encrypted_password,email_confirmed_at,invited_at,confirmation_token,confirmation_sent_at,recovery_token,recovery_sent_at,email_change_token_new,email_change,email_change_sent_at,last_sign_in_at,raw_app_meta_data,raw_user_meta_data,is_super_admin,created_at,updated_at,phone,phone_confirmed_at,phone_change,phone_change_token,phone_change_sent_at,email_change_token_current,email_change_confirm_status,banned_until,reauthentication_token,reauthentication_sent_at,is_sso_user,deleted_at,is_anonymous) VALUES ('00000000-0000-0000-0000-000000000000', 'fa4fd670-3418-599f-9a2a-e2b5bcf1d36c', 'authenticated', 'authenticated', '<EMAIL>', '', '2025-07-18T13:29:46.249Z', NULL, NULL, '2025-07-18T13:29:46.249Z', NULL, NULL, NULL, '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"email_verified":true}', NULL, '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', NULL, NULL, '', '', NULL, NULL, 0, NULL, NULL, NULL, 'f', NULL, 'f');
INSERT INTO auth.identities (provider_id,user_id,identity_data,provider,last_sign_in_at,created_at,updated_at,id) VALUES ('fa4fd670-3418-599f-9a2a-e2b5bcf1d36c', 'fa4fd670-3418-599f-9a2a-e2b5bcf1d36c', '{"sub":"fa4fd670-3418-599f-9a2a-e2b5bcf1d36c","email":"<EMAIL>","email_verified":true,"phone_verified":false}', 'email', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', 'cbd0e040-d74e-5f1c-8147-928841f7a612');
INSERT INTO public.profile (id,first_name,last_name,gender,phone_number,organization_id,role,created_at,created_by,updated_at,updated_by) VALUES ('fa4fd670-3418-599f-9a2a-e2b5bcf1d36c', 'Rosa', 'Weber', 'other', '+49527849438192', 'ba6ab718-5c91-5c6e-a145-8697208af538', 'tenant_seller', '2025-07-18T13:29:46.249Z', '3c87848e-3dee-520f-9edf-8736cdd05f8f', '2025-07-18T13:29:46.249Z', '3c87848e-3dee-520f-9edf-8736cdd05f8f');
INSERT INTO auth.users (instance_id,id,aud,role,email,encrypted_password,email_confirmed_at,invited_at,confirmation_token,confirmation_sent_at,recovery_token,recovery_sent_at,email_change_token_new,email_change,email_change_sent_at,last_sign_in_at,raw_app_meta_data,raw_user_meta_data,is_super_admin,created_at,updated_at,phone,phone_confirmed_at,phone_change,phone_change_token,phone_change_sent_at,email_change_token_current,email_change_confirm_status,banned_until,reauthentication_token,reauthentication_sent_at,is_sso_user,deleted_at,is_anonymous) VALUES ('00000000-0000-0000-0000-000000000000', '2df4cf95-e050-5222-b779-850fa4203f85', 'authenticated', 'authenticated', '<EMAIL>', '', '2025-07-18T13:29:46.249Z', NULL, NULL, '2025-07-18T13:29:46.249Z', NULL, NULL, NULL, '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"email_verified":true}', NULL, '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', NULL, NULL, '', '', NULL, NULL, 0, NULL, NULL, NULL, 'f', NULL, 'f');
INSERT INTO auth.identities (provider_id,user_id,identity_data,provider,last_sign_in_at,created_at,updated_at,id) VALUES ('2df4cf95-e050-5222-b779-850fa4203f85', '2df4cf95-e050-5222-b779-850fa4203f85', '{"sub":"2df4cf95-e050-5222-b779-850fa4203f85","email":"<EMAIL>","email_verified":true,"phone_verified":false}', 'email', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '68404e0e-350f-5920-b8df-c9169ae3e946');
INSERT INTO public.profile (id,first_name,last_name,gender,phone_number,organization_id,role,created_at,created_by,updated_at,updated_by) VALUES ('2df4cf95-e050-5222-b779-850fa4203f85', 'Ann', 'Schönherr', 'male', '+4932450764036', 'ba6ab718-5c91-5c6e-a145-8697208af538', 'tenant_seller', '2025-07-18T13:29:46.249Z', '3c87848e-3dee-520f-9edf-8736cdd05f8f', '2025-07-18T13:29:46.249Z', '3c87848e-3dee-520f-9edf-8736cdd05f8f');
INSERT INTO auth.users (instance_id,id,aud,role,email,encrypted_password,email_confirmed_at,invited_at,confirmation_token,confirmation_sent_at,recovery_token,recovery_sent_at,email_change_token_new,email_change,email_change_sent_at,last_sign_in_at,raw_app_meta_data,raw_user_meta_data,is_super_admin,created_at,updated_at,phone,phone_confirmed_at,phone_change,phone_change_token,phone_change_sent_at,email_change_token_current,email_change_confirm_status,banned_until,reauthentication_token,reauthentication_sent_at,is_sso_user,deleted_at,is_anonymous) VALUES ('00000000-0000-0000-0000-000000000000', '3184e099-622a-5276-b866-39e4d26b08c0', 'authenticated', 'authenticated', '<EMAIL>', '', '2025-07-18T13:29:46.249Z', NULL, NULL, '2025-07-18T13:29:46.249Z', NULL, NULL, NULL, '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"email_verified":true}', NULL, '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', NULL, NULL, '', '', NULL, NULL, 0, NULL, NULL, NULL, 'f', NULL, 'f');
INSERT INTO auth.identities (provider_id,user_id,identity_data,provider,last_sign_in_at,created_at,updated_at,id) VALUES ('3184e099-622a-5276-b866-39e4d26b08c0', '3184e099-622a-5276-b866-39e4d26b08c0', '{"sub":"3184e099-622a-5276-b866-39e4d26b08c0","email":"<EMAIL>","email_verified":true,"phone_verified":false}', 'email', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '060321f9-3f69-5407-a8fe-eba739ce9b5b');
INSERT INTO public.profile (id,first_name,last_name,gender,phone_number,organization_id,role,created_at,created_by,updated_at,updated_by) VALUES ('3184e099-622a-5276-b866-39e4d26b08c0', 'Iven', 'Schimmer', 'male', '+4918822224855', 'ba6ab718-5c91-5c6e-a145-8697208af538', 'tenant_compliance', '2025-07-18T13:29:46.249Z', '3c87848e-3dee-520f-9edf-8736cdd05f8f', '2025-07-18T13:29:46.249Z', '3c87848e-3dee-520f-9edf-8736cdd05f8f');
INSERT INTO public.organization (id,consultancy_organization_id,name,type,line1,line2,postal_code,city,state,country,created_at,created_by,updated_at,updated_by) VALUES ('6997bb10-5009-5b32-8bf8-7efd420bca54', NULL, 'Blum UG C1T3', 'tenant', 'Hammerweg 56b', NULL, '95583', 'West Thorbenscheid', 'Berlin', 'AT', '2025-07-18T13:29:46.249Z', '0f5d546a-dd80-5406-a005-a4f3061b9fb4', '2025-07-18T13:29:46.249Z', '0f5d546a-dd80-5406-a005-a4f3061b9fb4');
UPDATE public.organization SET consultancy_organization_id = 'c5bc6e6e-72ee-5b1a-9e63-65bf1e453498' WHERE id = '6997bb10-5009-5b32-8bf8-7efd420bca54';
INSERT INTO auth.users (instance_id,id,aud,role,email,encrypted_password,email_confirmed_at,invited_at,confirmation_token,confirmation_sent_at,recovery_token,recovery_sent_at,email_change_token_new,email_change,email_change_sent_at,last_sign_in_at,raw_app_meta_data,raw_user_meta_data,is_super_admin,created_at,updated_at,phone,phone_confirmed_at,phone_change,phone_change_token,phone_change_sent_at,email_change_token_current,email_change_confirm_status,banned_until,reauthentication_token,reauthentication_sent_at,is_sso_user,deleted_at,is_anonymous) VALUES ('00000000-0000-0000-0000-000000000000', 'dee7b59e-1b61-55e6-a68e-cacca9104b2c', 'authenticated', 'authenticated', '<EMAIL>', '', '2025-07-18T13:29:46.249Z', NULL, NULL, '2025-07-18T13:29:46.249Z', NULL, NULL, NULL, '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"email_verified":true}', NULL, '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', NULL, NULL, '', '', NULL, NULL, 0, NULL, NULL, NULL, 'f', NULL, 'f');
INSERT INTO auth.identities (provider_id,user_id,identity_data,provider,last_sign_in_at,created_at,updated_at,id) VALUES ('dee7b59e-1b61-55e6-a68e-cacca9104b2c', 'dee7b59e-1b61-55e6-a68e-cacca9104b2c', '{"sub":"dee7b59e-1b61-55e6-a68e-cacca9104b2c","email":"<EMAIL>","email_verified":true,"phone_verified":false}', 'email', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '1068c6b5-c1f1-5a2a-97e5-ef51d0f8f02b');
INSERT INTO public.profile (id,first_name,last_name,gender,phone_number,organization_id,role,created_at,created_by,updated_at,updated_by) VALUES ('dee7b59e-1b61-55e6-a68e-cacca9104b2c', 'Torben', 'Edorh', 'female', '+497713427921', '6997bb10-5009-5b32-8bf8-7efd420bca54', 'tenant_manager', '2025-07-18T13:29:46.249Z', '0f5d546a-dd80-5406-a005-a4f3061b9fb4', '2025-07-18T13:29:46.249Z', '0f5d546a-dd80-5406-a005-a4f3061b9fb4');
INSERT INTO auth.users (instance_id,id,aud,role,email,encrypted_password,email_confirmed_at,invited_at,confirmation_token,confirmation_sent_at,recovery_token,recovery_sent_at,email_change_token_new,email_change,email_change_sent_at,last_sign_in_at,raw_app_meta_data,raw_user_meta_data,is_super_admin,created_at,updated_at,phone,phone_confirmed_at,phone_change,phone_change_token,phone_change_sent_at,email_change_token_current,email_change_confirm_status,banned_until,reauthentication_token,reauthentication_sent_at,is_sso_user,deleted_at,is_anonymous) VALUES ('00000000-0000-0000-0000-000000000000', 'cfd8fb33-ddb4-518d-a928-ce88d13409f3', 'authenticated', 'authenticated', '<EMAIL>', '', '2025-07-18T13:29:46.249Z', NULL, NULL, '2025-07-18T13:29:46.249Z', NULL, NULL, NULL, '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"email_verified":true}', NULL, '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', NULL, NULL, '', '', NULL, NULL, 0, NULL, NULL, NULL, 'f', NULL, 'f');
INSERT INTO auth.identities (provider_id,user_id,identity_data,provider,last_sign_in_at,created_at,updated_at,id) VALUES ('cfd8fb33-ddb4-518d-a928-ce88d13409f3', 'cfd8fb33-ddb4-518d-a928-ce88d13409f3', '{"sub":"cfd8fb33-ddb4-518d-a928-ce88d13409f3","email":"<EMAIL>","email_verified":true,"phone_verified":false}', 'email', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '6c5093f8-7ad7-5755-95ba-ee840928152f');
INSERT INTO public.profile (id,first_name,last_name,gender,phone_number,organization_id,role,created_at,created_by,updated_at,updated_by) VALUES ('cfd8fb33-ddb4-518d-a928-ce88d13409f3', 'Aurelia', 'Wallstab', 'male', '+49215801167883', '6997bb10-5009-5b32-8bf8-7efd420bca54', 'tenant_compliance', '2025-07-18T13:29:46.249Z', 'dee7b59e-1b61-55e6-a68e-cacca9104b2c', '2025-07-18T13:29:46.249Z', 'dee7b59e-1b61-55e6-a68e-cacca9104b2c');
INSERT INTO auth.users (instance_id,id,aud,role,email,encrypted_password,email_confirmed_at,invited_at,confirmation_token,confirmation_sent_at,recovery_token,recovery_sent_at,email_change_token_new,email_change,email_change_sent_at,last_sign_in_at,raw_app_meta_data,raw_user_meta_data,is_super_admin,created_at,updated_at,phone,phone_confirmed_at,phone_change,phone_change_token,phone_change_sent_at,email_change_token_current,email_change_confirm_status,banned_until,reauthentication_token,reauthentication_sent_at,is_sso_user,deleted_at,is_anonymous) VALUES ('00000000-0000-0000-0000-000000000000', '8fcbd6c5-6fb8-52e4-b1eb-ed896a9c9af1', 'authenticated', 'authenticated', '<EMAIL>', '', '2025-07-18T13:29:46.249Z', NULL, NULL, '2025-07-18T13:29:46.249Z', NULL, NULL, NULL, '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"email_verified":true}', NULL, '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', NULL, NULL, '', '', NULL, NULL, 0, NULL, NULL, NULL, 'f', NULL, 'f');
INSERT INTO auth.identities (provider_id,user_id,identity_data,provider,last_sign_in_at,created_at,updated_at,id) VALUES ('8fcbd6c5-6fb8-52e4-b1eb-ed896a9c9af1', '8fcbd6c5-6fb8-52e4-b1eb-ed896a9c9af1', '{"sub":"8fcbd6c5-6fb8-52e4-b1eb-ed896a9c9af1","email":"<EMAIL>","email_verified":true,"phone_verified":false}', 'email', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', 'f47bf224-9166-56f4-aa18-fad5499f5f83');
INSERT INTO public.profile (id,first_name,last_name,gender,phone_number,organization_id,role,created_at,created_by,updated_at,updated_by) VALUES ('8fcbd6c5-6fb8-52e4-b1eb-ed896a9c9af1', 'Curt', 'Gehring', 'male', '+49205763260023', '6997bb10-5009-5b32-8bf8-7efd420bca54', 'tenant_purchaser', '2025-07-18T13:29:46.249Z', 'dee7b59e-1b61-55e6-a68e-cacca9104b2c', '2025-07-18T13:29:46.249Z', 'dee7b59e-1b61-55e6-a68e-cacca9104b2c');
INSERT INTO auth.users (instance_id,id,aud,role,email,encrypted_password,email_confirmed_at,invited_at,confirmation_token,confirmation_sent_at,recovery_token,recovery_sent_at,email_change_token_new,email_change,email_change_sent_at,last_sign_in_at,raw_app_meta_data,raw_user_meta_data,is_super_admin,created_at,updated_at,phone,phone_confirmed_at,phone_change,phone_change_token,phone_change_sent_at,email_change_token_current,email_change_confirm_status,banned_until,reauthentication_token,reauthentication_sent_at,is_sso_user,deleted_at,is_anonymous) VALUES ('00000000-0000-0000-0000-000000000000', '92e09312-1cfb-555d-94bf-f5156a4179ea', 'authenticated', 'authenticated', '<EMAIL>', '', '2025-07-18T13:29:46.249Z', NULL, NULL, '2025-07-18T13:29:46.249Z', NULL, NULL, NULL, '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"email_verified":true}', NULL, '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', NULL, NULL, '', '', NULL, NULL, 0, NULL, NULL, NULL, 'f', NULL, 'f');
INSERT INTO auth.identities (provider_id,user_id,identity_data,provider,last_sign_in_at,created_at,updated_at,id) VALUES ('92e09312-1cfb-555d-94bf-f5156a4179ea', '92e09312-1cfb-555d-94bf-f5156a4179ea', '{"sub":"92e09312-1cfb-555d-94bf-f5156a4179ea","email":"<EMAIL>","email_verified":true,"phone_verified":false}', 'email', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', 'be463c9a-b4e9-59b2-b986-f207fcbc06d0');
INSERT INTO public.profile (id,first_name,last_name,gender,phone_number,organization_id,role,created_at,created_by,updated_at,updated_by) VALUES ('92e09312-1cfb-555d-94bf-f5156a4179ea', 'Lily', 'Kral', 'male', '+4990068646780', '6997bb10-5009-5b32-8bf8-7efd420bca54', 'tenant_compliance', '2025-07-18T13:29:46.249Z', 'dee7b59e-1b61-55e6-a68e-cacca9104b2c', '2025-07-18T13:29:46.249Z', 'dee7b59e-1b61-55e6-a68e-cacca9104b2c');
INSERT INTO auth.users (instance_id,id,aud,role,email,encrypted_password,email_confirmed_at,invited_at,confirmation_token,confirmation_sent_at,recovery_token,recovery_sent_at,email_change_token_new,email_change,email_change_sent_at,last_sign_in_at,raw_app_meta_data,raw_user_meta_data,is_super_admin,created_at,updated_at,phone,phone_confirmed_at,phone_change,phone_change_token,phone_change_sent_at,email_change_token_current,email_change_confirm_status,banned_until,reauthentication_token,reauthentication_sent_at,is_sso_user,deleted_at,is_anonymous) VALUES ('00000000-0000-0000-0000-000000000000', 'dc6a1bc5-eab9-5e4e-bfec-6c7f31b2f09d', 'authenticated', 'authenticated', '<EMAIL>', '', '2025-07-18T13:29:46.249Z', NULL, NULL, '2025-07-18T13:29:46.249Z', NULL, NULL, NULL, '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"email_verified":true}', NULL, '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', NULL, NULL, '', '', NULL, NULL, 0, NULL, NULL, NULL, 'f', NULL, 'f');
INSERT INTO auth.identities (provider_id,user_id,identity_data,provider,last_sign_in_at,created_at,updated_at,id) VALUES ('dc6a1bc5-eab9-5e4e-bfec-6c7f31b2f09d', 'dc6a1bc5-eab9-5e4e-bfec-6c7f31b2f09d', '{"sub":"dc6a1bc5-eab9-5e4e-bfec-6c7f31b2f09d","email":"<EMAIL>","email_verified":true,"phone_verified":false}', 'email', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', 'cf6d2e4a-c3ba-5352-b5fc-159f8bb9c228');
INSERT INTO public.profile (id,first_name,last_name,gender,phone_number,organization_id,role,created_at,created_by,updated_at,updated_by) VALUES ('dc6a1bc5-eab9-5e4e-bfec-6c7f31b2f09d', 'Joline', 'Grimm', 'female', '+498660620377', '6997bb10-5009-5b32-8bf8-7efd420bca54', 'tenant_purchaser', '2025-07-18T13:29:46.249Z', 'dee7b59e-1b61-55e6-a68e-cacca9104b2c', '2025-07-18T13:29:46.249Z', 'dee7b59e-1b61-55e6-a68e-cacca9104b2c');
INSERT INTO auth.users (instance_id,id,aud,role,email,encrypted_password,email_confirmed_at,invited_at,confirmation_token,confirmation_sent_at,recovery_token,recovery_sent_at,email_change_token_new,email_change,email_change_sent_at,last_sign_in_at,raw_app_meta_data,raw_user_meta_data,is_super_admin,created_at,updated_at,phone,phone_confirmed_at,phone_change,phone_change_token,phone_change_sent_at,email_change_token_current,email_change_confirm_status,banned_until,reauthentication_token,reauthentication_sent_at,is_sso_user,deleted_at,is_anonymous) VALUES ('00000000-0000-0000-0000-000000000000', '7393b193-9040-56bf-ab02-0ba96e328a9a', 'authenticated', 'authenticated', '<EMAIL>', '', '2025-07-18T13:29:46.249Z', NULL, NULL, '2025-07-18T13:29:46.249Z', NULL, NULL, NULL, '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"email_verified":true}', NULL, '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', NULL, NULL, '', '', NULL, NULL, 0, NULL, NULL, NULL, 'f', NULL, 'f');
INSERT INTO auth.identities (provider_id,user_id,identity_data,provider,last_sign_in_at,created_at,updated_at,id) VALUES ('7393b193-9040-56bf-ab02-0ba96e328a9a', '7393b193-9040-56bf-ab02-0ba96e328a9a', '{"sub":"7393b193-9040-56bf-ab02-0ba96e328a9a","email":"<EMAIL>","email_verified":true,"phone_verified":false}', 'email', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', 'aad48be7-2aea-5515-9576-9e575ff1ef79');
INSERT INTO public.profile (id,first_name,last_name,gender,phone_number,organization_id,role,created_at,created_by,updated_at,updated_by) VALUES ('7393b193-9040-56bf-ab02-0ba96e328a9a', 'Maxima', 'Vogt', 'female', '+4920549221737', '6997bb10-5009-5b32-8bf8-7efd420bca54', 'tenant_compliance', '2025-07-18T13:29:46.249Z', 'dee7b59e-1b61-55e6-a68e-cacca9104b2c', '2025-07-18T13:29:46.249Z', 'dee7b59e-1b61-55e6-a68e-cacca9104b2c');
INSERT INTO auth.users (instance_id,id,aud,role,email,encrypted_password,email_confirmed_at,invited_at,confirmation_token,confirmation_sent_at,recovery_token,recovery_sent_at,email_change_token_new,email_change,email_change_sent_at,last_sign_in_at,raw_app_meta_data,raw_user_meta_data,is_super_admin,created_at,updated_at,phone,phone_confirmed_at,phone_change,phone_change_token,phone_change_sent_at,email_change_token_current,email_change_confirm_status,banned_until,reauthentication_token,reauthentication_sent_at,is_sso_user,deleted_at,is_anonymous) VALUES ('00000000-0000-0000-0000-000000000000', '9e54a714-95b6-5f5a-97e5-8d81b1107541', 'authenticated', 'authenticated', '<EMAIL>', '', '2025-07-18T13:29:46.249Z', NULL, NULL, '2025-07-18T13:29:46.249Z', NULL, NULL, NULL, '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"email_verified":true}', NULL, '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', NULL, NULL, '', '', NULL, NULL, 0, NULL, NULL, NULL, 'f', NULL, 'f');
INSERT INTO auth.identities (provider_id,user_id,identity_data,provider,last_sign_in_at,created_at,updated_at,id) VALUES ('9e54a714-95b6-5f5a-97e5-8d81b1107541', '9e54a714-95b6-5f5a-97e5-8d81b1107541', '{"sub":"9e54a714-95b6-5f5a-97e5-8d81b1107541","email":"<EMAIL>","email_verified":true,"phone_verified":false}', 'email', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', 'eafe73ea-ac5f-5848-9cc3-0d16cca406f1');
INSERT INTO public.organization (id,consultancy_organization_id,name,type,line1,line2,postal_code,city,state,country,created_at,created_by,updated_at,updated_by) VALUES ('41f9aebe-f1ab-59f9-b0d6-b0968a4edb66', NULL, 'Bauschke, Vokuhl und Frohn 2', 'consultancy', 'Edelrather Weg 15', NULL, '27338', 'Alt Lorena', 'Nordrhein-Westfalen', 'AT', '2025-07-18T13:29:46.249Z', '9e54a714-95b6-5f5a-97e5-8d81b1107541', '2025-07-18T13:29:46.249Z', '9e54a714-95b6-5f5a-97e5-8d81b1107541');
INSERT INTO public.profile (id,first_name,last_name,gender,phone_number,organization_id,role,created_at,created_by,updated_at,updated_by) VALUES ('9e54a714-95b6-5f5a-97e5-8d81b1107541', 'Emma', 'Töpfer', 'female', '+49267165185078', '41f9aebe-f1ab-59f9-b0d6-b0968a4edb66', 'consultancy_manager', '2025-07-18T13:29:46.249Z', '9e54a714-95b6-5f5a-97e5-8d81b1107541', '2025-07-18T13:29:46.249Z', '9e54a714-95b6-5f5a-97e5-8d81b1107541');
INSERT INTO public.organization (id,consultancy_organization_id,name,type,line1,line2,postal_code,city,state,country,created_at,created_by,updated_at,updated_by) VALUES ('91a5bad0-b5db-546a-9284-859f8e64b925', NULL, 'Klose KG C2T1', 'tenant', 'Gustav-Freytag-Str. 48b', NULL, '53119', 'Nord Yannicland', 'Nordrhein-Westfalen', 'CH', '2025-07-18T13:29:46.249Z', '9e54a714-95b6-5f5a-97e5-8d81b1107541', '2025-07-18T13:29:46.249Z', '9e54a714-95b6-5f5a-97e5-8d81b1107541');
UPDATE public.organization SET consultancy_organization_id = '41f9aebe-f1ab-59f9-b0d6-b0968a4edb66' WHERE id = '91a5bad0-b5db-546a-9284-859f8e64b925';
INSERT INTO auth.users (instance_id,id,aud,role,email,encrypted_password,email_confirmed_at,invited_at,confirmation_token,confirmation_sent_at,recovery_token,recovery_sent_at,email_change_token_new,email_change,email_change_sent_at,last_sign_in_at,raw_app_meta_data,raw_user_meta_data,is_super_admin,created_at,updated_at,phone,phone_confirmed_at,phone_change,phone_change_token,phone_change_sent_at,email_change_token_current,email_change_confirm_status,banned_until,reauthentication_token,reauthentication_sent_at,is_sso_user,deleted_at,is_anonymous) VALUES ('00000000-0000-0000-0000-000000000000', '7114abda-03e1-52c9-b193-5cfd8f930a9e', 'authenticated', 'authenticated', '<EMAIL>', '', '2025-07-18T13:29:46.249Z', NULL, NULL, '2025-07-18T13:29:46.249Z', NULL, NULL, NULL, '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"email_verified":true}', NULL, '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', NULL, NULL, '', '', NULL, NULL, 0, NULL, NULL, NULL, 'f', NULL, 'f');
INSERT INTO auth.identities (provider_id,user_id,identity_data,provider,last_sign_in_at,created_at,updated_at,id) VALUES ('7114abda-03e1-52c9-b193-5cfd8f930a9e', '7114abda-03e1-52c9-b193-5cfd8f930a9e', '{"sub":"7114abda-03e1-52c9-b193-5cfd8f930a9e","email":"<EMAIL>","email_verified":true,"phone_verified":false}', 'email', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '78b4271a-969b-5af5-9e62-5feabd5dc6bc');
INSERT INTO public.profile (id,first_name,last_name,gender,phone_number,organization_id,role,created_at,created_by,updated_at,updated_by) VALUES ('7114abda-03e1-52c9-b193-5cfd8f930a9e', 'Colin', 'Hodea', 'female', '+4962392502783', '91a5bad0-b5db-546a-9284-859f8e64b925', 'tenant_manager', '2025-07-18T13:29:46.249Z', '9e54a714-95b6-5f5a-97e5-8d81b1107541', '2025-07-18T13:29:46.249Z', '9e54a714-95b6-5f5a-97e5-8d81b1107541');
INSERT INTO auth.users (instance_id,id,aud,role,email,encrypted_password,email_confirmed_at,invited_at,confirmation_token,confirmation_sent_at,recovery_token,recovery_sent_at,email_change_token_new,email_change,email_change_sent_at,last_sign_in_at,raw_app_meta_data,raw_user_meta_data,is_super_admin,created_at,updated_at,phone,phone_confirmed_at,phone_change,phone_change_token,phone_change_sent_at,email_change_token_current,email_change_confirm_status,banned_until,reauthentication_token,reauthentication_sent_at,is_sso_user,deleted_at,is_anonymous) VALUES ('00000000-0000-0000-0000-000000000000', '64f848cd-fc4e-52b5-b943-c9d04f3c8eaa', 'authenticated', 'authenticated', '<EMAIL>', '', '2025-07-18T13:29:46.249Z', NULL, NULL, '2025-07-18T13:29:46.249Z', NULL, NULL, NULL, '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"email_verified":true}', NULL, '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', NULL, NULL, '', '', NULL, NULL, 0, NULL, NULL, NULL, 'f', NULL, 'f');
INSERT INTO auth.identities (provider_id,user_id,identity_data,provider,last_sign_in_at,created_at,updated_at,id) VALUES ('64f848cd-fc4e-52b5-b943-c9d04f3c8eaa', '64f848cd-fc4e-52b5-b943-c9d04f3c8eaa', '{"sub":"64f848cd-fc4e-52b5-b943-c9d04f3c8eaa","email":"<EMAIL>","email_verified":true,"phone_verified":false}', 'email', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', 'ba7e220f-150b-57fc-bd39-ecaea6b50746');
INSERT INTO public.profile (id,first_name,last_name,gender,phone_number,organization_id,role,created_at,created_by,updated_at,updated_by) VALUES ('64f848cd-fc4e-52b5-b943-c9d04f3c8eaa', 'Rania', 'Brömme', 'other', '+49820150755139', '91a5bad0-b5db-546a-9284-859f8e64b925', 'tenant_seller', '2025-07-18T13:29:46.249Z', '7114abda-03e1-52c9-b193-5cfd8f930a9e', '2025-07-18T13:29:46.249Z', '7114abda-03e1-52c9-b193-5cfd8f930a9e');
INSERT INTO auth.users (instance_id,id,aud,role,email,encrypted_password,email_confirmed_at,invited_at,confirmation_token,confirmation_sent_at,recovery_token,recovery_sent_at,email_change_token_new,email_change,email_change_sent_at,last_sign_in_at,raw_app_meta_data,raw_user_meta_data,is_super_admin,created_at,updated_at,phone,phone_confirmed_at,phone_change,phone_change_token,phone_change_sent_at,email_change_token_current,email_change_confirm_status,banned_until,reauthentication_token,reauthentication_sent_at,is_sso_user,deleted_at,is_anonymous) VALUES ('00000000-0000-0000-0000-000000000000', '9c5385fb-0964-51a3-a527-b2e5422636eb', 'authenticated', 'authenticated', '<EMAIL>', '', '2025-07-18T13:29:46.249Z', NULL, NULL, '2025-07-18T13:29:46.249Z', NULL, NULL, NULL, '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"email_verified":true}', NULL, '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', NULL, NULL, '', '', NULL, NULL, 0, NULL, NULL, NULL, 'f', NULL, 'f');
INSERT INTO auth.identities (provider_id,user_id,identity_data,provider,last_sign_in_at,created_at,updated_at,id) VALUES ('9c5385fb-0964-51a3-a527-b2e5422636eb', '9c5385fb-0964-51a3-a527-b2e5422636eb', '{"sub":"9c5385fb-0964-51a3-a527-b2e5422636eb","email":"<EMAIL>","email_verified":true,"phone_verified":false}', 'email', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', 'dc0b1dc4-1588-5bed-90ed-5a751d052e29');
INSERT INTO public.profile (id,first_name,last_name,gender,phone_number,organization_id,role,created_at,created_by,updated_at,updated_by) VALUES ('9c5385fb-0964-51a3-a527-b2e5422636eb', 'Emelie', 'Dragu', 'other', '+4945282785987', '91a5bad0-b5db-546a-9284-859f8e64b925', 'tenant_purchaser', '2025-07-18T13:29:46.249Z', '7114abda-03e1-52c9-b193-5cfd8f930a9e', '2025-07-18T13:29:46.249Z', '7114abda-03e1-52c9-b193-5cfd8f930a9e');
INSERT INTO auth.users (instance_id,id,aud,role,email,encrypted_password,email_confirmed_at,invited_at,confirmation_token,confirmation_sent_at,recovery_token,recovery_sent_at,email_change_token_new,email_change,email_change_sent_at,last_sign_in_at,raw_app_meta_data,raw_user_meta_data,is_super_admin,created_at,updated_at,phone,phone_confirmed_at,phone_change,phone_change_token,phone_change_sent_at,email_change_token_current,email_change_confirm_status,banned_until,reauthentication_token,reauthentication_sent_at,is_sso_user,deleted_at,is_anonymous) VALUES ('00000000-0000-0000-0000-000000000000', '8e8e2374-00e6-5f2b-8995-f7de758c12fa', 'authenticated', 'authenticated', '<EMAIL>', '', '2025-07-18T13:29:46.249Z', NULL, NULL, '2025-07-18T13:29:46.249Z', NULL, NULL, NULL, '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"email_verified":true}', NULL, '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', NULL, NULL, '', '', NULL, NULL, 0, NULL, NULL, NULL, 'f', NULL, 'f');
INSERT INTO auth.identities (provider_id,user_id,identity_data,provider,last_sign_in_at,created_at,updated_at,id) VALUES ('8e8e2374-00e6-5f2b-8995-f7de758c12fa', '8e8e2374-00e6-5f2b-8995-f7de758c12fa', '{"sub":"8e8e2374-00e6-5f2b-8995-f7de758c12fa","email":"<EMAIL>","email_verified":true,"phone_verified":false}', 'email', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '265a9f9b-d720-5ccd-98bd-c99ab7491049');
INSERT INTO public.profile (id,first_name,last_name,gender,phone_number,organization_id,role,created_at,created_by,updated_at,updated_by) VALUES ('8e8e2374-00e6-5f2b-8995-f7de758c12fa', 'Yannik', 'Knoll', 'other', '+4980367195547', '91a5bad0-b5db-546a-9284-859f8e64b925', 'tenant_compliance', '2025-07-18T13:29:46.249Z', '7114abda-03e1-52c9-b193-5cfd8f930a9e', '2025-07-18T13:29:46.249Z', '7114abda-03e1-52c9-b193-5cfd8f930a9e');
INSERT INTO auth.users (instance_id,id,aud,role,email,encrypted_password,email_confirmed_at,invited_at,confirmation_token,confirmation_sent_at,recovery_token,recovery_sent_at,email_change_token_new,email_change,email_change_sent_at,last_sign_in_at,raw_app_meta_data,raw_user_meta_data,is_super_admin,created_at,updated_at,phone,phone_confirmed_at,phone_change,phone_change_token,phone_change_sent_at,email_change_token_current,email_change_confirm_status,banned_until,reauthentication_token,reauthentication_sent_at,is_sso_user,deleted_at,is_anonymous) VALUES ('00000000-0000-0000-0000-000000000000', 'dfdcfcfd-48e8-5798-95e0-9019660a15b6', 'authenticated', 'authenticated', '<EMAIL>', '', '2025-07-18T13:29:46.249Z', NULL, NULL, '2025-07-18T13:29:46.249Z', NULL, NULL, NULL, '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"email_verified":true}', NULL, '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', NULL, NULL, '', '', NULL, NULL, 0, NULL, NULL, NULL, 'f', NULL, 'f');
INSERT INTO auth.identities (provider_id,user_id,identity_data,provider,last_sign_in_at,created_at,updated_at,id) VALUES ('dfdcfcfd-48e8-5798-95e0-9019660a15b6', 'dfdcfcfd-48e8-5798-95e0-9019660a15b6', '{"sub":"dfdcfcfd-48e8-5798-95e0-9019660a15b6","email":"<EMAIL>","email_verified":true,"phone_verified":false}', 'email', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '5a269d28-0a2f-51b6-aee7-952e259ca935');
INSERT INTO public.profile (id,first_name,last_name,gender,phone_number,organization_id,role,created_at,created_by,updated_at,updated_by) VALUES ('dfdcfcfd-48e8-5798-95e0-9019660a15b6', 'Lillian', 'Mues', 'female', '+4905584924836', '91a5bad0-b5db-546a-9284-859f8e64b925', 'tenant_seller', '2025-07-18T13:29:46.249Z', '7114abda-03e1-52c9-b193-5cfd8f930a9e', '2025-07-18T13:29:46.249Z', '7114abda-03e1-52c9-b193-5cfd8f930a9e');
INSERT INTO auth.users (instance_id,id,aud,role,email,encrypted_password,email_confirmed_at,invited_at,confirmation_token,confirmation_sent_at,recovery_token,recovery_sent_at,email_change_token_new,email_change,email_change_sent_at,last_sign_in_at,raw_app_meta_data,raw_user_meta_data,is_super_admin,created_at,updated_at,phone,phone_confirmed_at,phone_change,phone_change_token,phone_change_sent_at,email_change_token_current,email_change_confirm_status,banned_until,reauthentication_token,reauthentication_sent_at,is_sso_user,deleted_at,is_anonymous) VALUES ('00000000-0000-0000-0000-000000000000', '5cb98afe-7398-577d-917b-d8cef065f251', 'authenticated', 'authenticated', '<EMAIL>', '', '2025-07-18T13:29:46.249Z', NULL, NULL, '2025-07-18T13:29:46.249Z', NULL, NULL, NULL, '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"email_verified":true}', NULL, '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', NULL, NULL, '', '', NULL, NULL, 0, NULL, NULL, NULL, 'f', NULL, 'f');
INSERT INTO auth.identities (provider_id,user_id,identity_data,provider,last_sign_in_at,created_at,updated_at,id) VALUES ('5cb98afe-7398-577d-917b-d8cef065f251', '5cb98afe-7398-577d-917b-d8cef065f251', '{"sub":"5cb98afe-7398-577d-917b-d8cef065f251","email":"<EMAIL>","email_verified":true,"phone_verified":false}', 'email', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', 'b0726e1d-89ea-5854-ad27-cb5a8dd76727');
INSERT INTO public.profile (id,first_name,last_name,gender,phone_number,organization_id,role,created_at,created_by,updated_at,updated_by) VALUES ('5cb98afe-7398-577d-917b-d8cef065f251', 'Zara', 'Nolte', 'male', '+4930196112735', '91a5bad0-b5db-546a-9284-859f8e64b925', 'tenant_seller', '2025-07-18T13:29:46.249Z', '7114abda-03e1-52c9-b193-5cfd8f930a9e', '2025-07-18T13:29:46.249Z', '7114abda-03e1-52c9-b193-5cfd8f930a9e');
INSERT INTO public.organization (id,consultancy_organization_id,name,type,line1,line2,postal_code,city,state,country,created_at,created_by,updated_at,updated_by) VALUES ('2f0a5288-3870-5c5f-9c75-26a9b419f4bd', NULL, 'Kozakiewicz-Klopsch C2T2', 'tenant', 'Burscheider Str. 44a', NULL, '56131', 'Cheyenneland', 'Schleswig-Holstein', 'CH', '2025-07-18T13:29:46.249Z', '9e54a714-95b6-5f5a-97e5-8d81b1107541', '2025-07-18T13:29:46.249Z', '9e54a714-95b6-5f5a-97e5-8d81b1107541');
UPDATE public.organization SET consultancy_organization_id = '41f9aebe-f1ab-59f9-b0d6-b0968a4edb66' WHERE id = '2f0a5288-3870-5c5f-9c75-26a9b419f4bd';
INSERT INTO auth.users (instance_id,id,aud,role,email,encrypted_password,email_confirmed_at,invited_at,confirmation_token,confirmation_sent_at,recovery_token,recovery_sent_at,email_change_token_new,email_change,email_change_sent_at,last_sign_in_at,raw_app_meta_data,raw_user_meta_data,is_super_admin,created_at,updated_at,phone,phone_confirmed_at,phone_change,phone_change_token,phone_change_sent_at,email_change_token_current,email_change_confirm_status,banned_until,reauthentication_token,reauthentication_sent_at,is_sso_user,deleted_at,is_anonymous) VALUES ('00000000-0000-0000-0000-000000000000', 'a668c58b-8752-5bb2-b0e7-7bad5cea2922', 'authenticated', 'authenticated', '<EMAIL>', '', '2025-07-18T13:29:46.249Z', NULL, NULL, '2025-07-18T13:29:46.249Z', NULL, NULL, NULL, '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"email_verified":true}', NULL, '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', NULL, NULL, '', '', NULL, NULL, 0, NULL, NULL, NULL, 'f', NULL, 'f');
INSERT INTO auth.identities (provider_id,user_id,identity_data,provider,last_sign_in_at,created_at,updated_at,id) VALUES ('a668c58b-8752-5bb2-b0e7-7bad5cea2922', 'a668c58b-8752-5bb2-b0e7-7bad5cea2922', '{"sub":"a668c58b-8752-5bb2-b0e7-7bad5cea2922","email":"<EMAIL>","email_verified":true,"phone_verified":false}', 'email', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '8e8ecaf9-ff66-5d60-a501-da13ac1a97e4');
INSERT INTO public.profile (id,first_name,last_name,gender,phone_number,organization_id,role,created_at,created_by,updated_at,updated_by) VALUES ('a668c58b-8752-5bb2-b0e7-7bad5cea2922', 'Fabian', 'Schramm', 'male', '+495911724324', '2f0a5288-3870-5c5f-9c75-26a9b419f4bd', 'tenant_manager', '2025-07-18T13:29:46.249Z', '9e54a714-95b6-5f5a-97e5-8d81b1107541', '2025-07-18T13:29:46.249Z', '9e54a714-95b6-5f5a-97e5-8d81b1107541');
INSERT INTO auth.users (instance_id,id,aud,role,email,encrypted_password,email_confirmed_at,invited_at,confirmation_token,confirmation_sent_at,recovery_token,recovery_sent_at,email_change_token_new,email_change,email_change_sent_at,last_sign_in_at,raw_app_meta_data,raw_user_meta_data,is_super_admin,created_at,updated_at,phone,phone_confirmed_at,phone_change,phone_change_token,phone_change_sent_at,email_change_token_current,email_change_confirm_status,banned_until,reauthentication_token,reauthentication_sent_at,is_sso_user,deleted_at,is_anonymous) VALUES ('00000000-0000-0000-0000-000000000000', 'f1a31d2c-3a94-5cb3-9a54-5925b22ee496', 'authenticated', 'authenticated', '<EMAIL>', '', '2025-07-18T13:29:46.249Z', NULL, NULL, '2025-07-18T13:29:46.249Z', NULL, NULL, NULL, '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"email_verified":true}', NULL, '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', NULL, NULL, '', '', NULL, NULL, 0, NULL, NULL, NULL, 'f', NULL, 'f');
INSERT INTO auth.identities (provider_id,user_id,identity_data,provider,last_sign_in_at,created_at,updated_at,id) VALUES ('f1a31d2c-3a94-5cb3-9a54-5925b22ee496', 'f1a31d2c-3a94-5cb3-9a54-5925b22ee496', '{"sub":"f1a31d2c-3a94-5cb3-9a54-5925b22ee496","email":"<EMAIL>","email_verified":true,"phone_verified":false}', 'email', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '2a9b8dea-5557-5332-b6f7-11bb4d23cdc8');
INSERT INTO public.profile (id,first_name,last_name,gender,phone_number,organization_id,role,created_at,created_by,updated_at,updated_by) VALUES ('f1a31d2c-3a94-5cb3-9a54-5925b22ee496', 'Tamia', 'Jungbluth', 'other', '+4920883353662', '2f0a5288-3870-5c5f-9c75-26a9b419f4bd', 'tenant_purchaser', '2025-07-18T13:29:46.249Z', 'a668c58b-8752-5bb2-b0e7-7bad5cea2922', '2025-07-18T13:29:46.249Z', 'a668c58b-8752-5bb2-b0e7-7bad5cea2922');
INSERT INTO auth.users (instance_id,id,aud,role,email,encrypted_password,email_confirmed_at,invited_at,confirmation_token,confirmation_sent_at,recovery_token,recovery_sent_at,email_change_token_new,email_change,email_change_sent_at,last_sign_in_at,raw_app_meta_data,raw_user_meta_data,is_super_admin,created_at,updated_at,phone,phone_confirmed_at,phone_change,phone_change_token,phone_change_sent_at,email_change_token_current,email_change_confirm_status,banned_until,reauthentication_token,reauthentication_sent_at,is_sso_user,deleted_at,is_anonymous) VALUES ('00000000-0000-0000-0000-000000000000', '2132bc13-ccf7-527b-8b4a-55411093c807', 'authenticated', 'authenticated', '<EMAIL>', '', '2025-07-18T13:29:46.249Z', NULL, NULL, '2025-07-18T13:29:46.249Z', NULL, NULL, NULL, '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"email_verified":true}', NULL, '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', NULL, NULL, '', '', NULL, NULL, 0, NULL, NULL, NULL, 'f', NULL, 'f');
INSERT INTO auth.identities (provider_id,user_id,identity_data,provider,last_sign_in_at,created_at,updated_at,id) VALUES ('2132bc13-ccf7-527b-8b4a-55411093c807', '2132bc13-ccf7-527b-8b4a-55411093c807', '{"sub":"2132bc13-ccf7-527b-8b4a-55411093c807","email":"<EMAIL>","email_verified":true,"phone_verified":false}', 'email', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '9b31d455-ebe9-5783-b1cf-516def6c1b80');
INSERT INTO public.profile (id,first_name,last_name,gender,phone_number,organization_id,role,created_at,created_by,updated_at,updated_by) VALUES ('2132bc13-ccf7-527b-8b4a-55411093c807', 'Hannes', 'Mohr', 'other', '+49297487931170', '2f0a5288-3870-5c5f-9c75-26a9b419f4bd', 'tenant_seller', '2025-07-18T13:29:46.249Z', 'a668c58b-8752-5bb2-b0e7-7bad5cea2922', '2025-07-18T13:29:46.249Z', 'a668c58b-8752-5bb2-b0e7-7bad5cea2922');
INSERT INTO auth.users (instance_id,id,aud,role,email,encrypted_password,email_confirmed_at,invited_at,confirmation_token,confirmation_sent_at,recovery_token,recovery_sent_at,email_change_token_new,email_change,email_change_sent_at,last_sign_in_at,raw_app_meta_data,raw_user_meta_data,is_super_admin,created_at,updated_at,phone,phone_confirmed_at,phone_change,phone_change_token,phone_change_sent_at,email_change_token_current,email_change_confirm_status,banned_until,reauthentication_token,reauthentication_sent_at,is_sso_user,deleted_at,is_anonymous) VALUES ('00000000-0000-0000-0000-000000000000', 'b85b9bdd-a07f-544d-9b6f-794ac05a3fff', 'authenticated', 'authenticated', '<EMAIL>', '', '2025-07-18T13:29:46.249Z', NULL, NULL, '2025-07-18T13:29:46.249Z', NULL, NULL, NULL, '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"email_verified":true}', NULL, '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', NULL, NULL, '', '', NULL, NULL, 0, NULL, NULL, NULL, 'f', NULL, 'f');
INSERT INTO auth.identities (provider_id,user_id,identity_data,provider,last_sign_in_at,created_at,updated_at,id) VALUES ('b85b9bdd-a07f-544d-9b6f-794ac05a3fff', 'b85b9bdd-a07f-544d-9b6f-794ac05a3fff', '{"sub":"b85b9bdd-a07f-544d-9b6f-794ac05a3fff","email":"<EMAIL>","email_verified":true,"phone_verified":false}', 'email', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '20174010-d1bb-52d7-81c8-fc32f492e022');
INSERT INTO public.profile (id,first_name,last_name,gender,phone_number,organization_id,role,created_at,created_by,updated_at,updated_by) VALUES ('b85b9bdd-a07f-544d-9b6f-794ac05a3fff', 'Melike', 'Salzmann', 'male', '+4999641653883', '2f0a5288-3870-5c5f-9c75-26a9b419f4bd', 'tenant_purchaser', '2025-07-18T13:29:46.249Z', 'a668c58b-8752-5bb2-b0e7-7bad5cea2922', '2025-07-18T13:29:46.249Z', 'a668c58b-8752-5bb2-b0e7-7bad5cea2922');
INSERT INTO auth.users (instance_id,id,aud,role,email,encrypted_password,email_confirmed_at,invited_at,confirmation_token,confirmation_sent_at,recovery_token,recovery_sent_at,email_change_token_new,email_change,email_change_sent_at,last_sign_in_at,raw_app_meta_data,raw_user_meta_data,is_super_admin,created_at,updated_at,phone,phone_confirmed_at,phone_change,phone_change_token,phone_change_sent_at,email_change_token_current,email_change_confirm_status,banned_until,reauthentication_token,reauthentication_sent_at,is_sso_user,deleted_at,is_anonymous) VALUES ('00000000-0000-0000-0000-000000000000', 'ed54cb46-d3cd-5c26-905f-3bc3bed7fff6', 'authenticated', 'authenticated', '<EMAIL>', '', '2025-07-18T13:29:46.249Z', NULL, NULL, '2025-07-18T13:29:46.249Z', NULL, NULL, NULL, '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"email_verified":true}', NULL, '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', NULL, NULL, '', '', NULL, NULL, 0, NULL, NULL, NULL, 'f', NULL, 'f');
INSERT INTO auth.identities (provider_id,user_id,identity_data,provider,last_sign_in_at,created_at,updated_at,id) VALUES ('ed54cb46-d3cd-5c26-905f-3bc3bed7fff6', 'ed54cb46-d3cd-5c26-905f-3bc3bed7fff6', '{"sub":"ed54cb46-d3cd-5c26-905f-3bc3bed7fff6","email":"<EMAIL>","email_verified":true,"phone_verified":false}', 'email', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '2f2f09c1-72fe-53f1-8b76-ff7eb1e13f11');
INSERT INTO public.profile (id,first_name,last_name,gender,phone_number,organization_id,role,created_at,created_by,updated_at,updated_by) VALUES ('ed54cb46-d3cd-5c26-905f-3bc3bed7fff6', 'Calvin', 'Grosser', 'female', '+49689014559087', '2f0a5288-3870-5c5f-9c75-26a9b419f4bd', 'tenant_compliance', '2025-07-18T13:29:46.249Z', 'a668c58b-8752-5bb2-b0e7-7bad5cea2922', '2025-07-18T13:29:46.249Z', 'a668c58b-8752-5bb2-b0e7-7bad5cea2922');
INSERT INTO auth.users (instance_id,id,aud,role,email,encrypted_password,email_confirmed_at,invited_at,confirmation_token,confirmation_sent_at,recovery_token,recovery_sent_at,email_change_token_new,email_change,email_change_sent_at,last_sign_in_at,raw_app_meta_data,raw_user_meta_data,is_super_admin,created_at,updated_at,phone,phone_confirmed_at,phone_change,phone_change_token,phone_change_sent_at,email_change_token_current,email_change_confirm_status,banned_until,reauthentication_token,reauthentication_sent_at,is_sso_user,deleted_at,is_anonymous) VALUES ('00000000-0000-0000-0000-000000000000', '246ff867-98bd-5ae8-b7c7-f351d19e185c', 'authenticated', 'authenticated', '<EMAIL>', '', '2025-07-18T13:29:46.249Z', NULL, NULL, '2025-07-18T13:29:46.249Z', NULL, NULL, NULL, '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"email_verified":true}', NULL, '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', NULL, NULL, '', '', NULL, NULL, 0, NULL, NULL, NULL, 'f', NULL, 'f');
INSERT INTO auth.identities (provider_id,user_id,identity_data,provider,last_sign_in_at,created_at,updated_at,id) VALUES ('246ff867-98bd-5ae8-b7c7-f351d19e185c', '246ff867-98bd-5ae8-b7c7-f351d19e185c', '{"sub":"246ff867-98bd-5ae8-b7c7-f351d19e185c","email":"<EMAIL>","email_verified":true,"phone_verified":false}', 'email', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '6975e0b0-dc2f-50cd-8c1e-c06e7015d17b');
INSERT INTO public.profile (id,first_name,last_name,gender,phone_number,organization_id,role,created_at,created_by,updated_at,updated_by) VALUES ('246ff867-98bd-5ae8-b7c7-f351d19e185c', 'Daniel', 'Ponitzsch', 'male', '+4957188892476', '2f0a5288-3870-5c5f-9c75-26a9b419f4bd', 'tenant_compliance', '2025-07-18T13:29:46.249Z', 'a668c58b-8752-5bb2-b0e7-7bad5cea2922', '2025-07-18T13:29:46.249Z', 'a668c58b-8752-5bb2-b0e7-7bad5cea2922');
INSERT INTO public.organization (id,consultancy_organization_id,name,type,line1,line2,postal_code,city,state,country,created_at,created_by,updated_at,updated_by) VALUES ('fabdb6a3-b8ba-5cc9-9616-f82e61dc5da5', NULL, 'Einert UG C2T3', 'tenant', 'Adalbert-Stifter-Str. 76b', 'Apt. 240', '33855', 'West Caroline', 'Berlin', 'AT', '2025-07-18T13:29:46.249Z', '9e54a714-95b6-5f5a-97e5-8d81b1107541', '2025-07-18T13:29:46.249Z', '9e54a714-95b6-5f5a-97e5-8d81b1107541');
UPDATE public.organization SET consultancy_organization_id = '41f9aebe-f1ab-59f9-b0d6-b0968a4edb66' WHERE id = 'fabdb6a3-b8ba-5cc9-9616-f82e61dc5da5';
INSERT INTO auth.users (instance_id,id,aud,role,email,encrypted_password,email_confirmed_at,invited_at,confirmation_token,confirmation_sent_at,recovery_token,recovery_sent_at,email_change_token_new,email_change,email_change_sent_at,last_sign_in_at,raw_app_meta_data,raw_user_meta_data,is_super_admin,created_at,updated_at,phone,phone_confirmed_at,phone_change,phone_change_token,phone_change_sent_at,email_change_token_current,email_change_confirm_status,banned_until,reauthentication_token,reauthentication_sent_at,is_sso_user,deleted_at,is_anonymous) VALUES ('00000000-0000-0000-0000-000000000000', 'e9274bac-ba72-5320-8d7d-ab1f9d0ee317', 'authenticated', 'authenticated', '<EMAIL>', '', '2025-07-18T13:29:46.249Z', NULL, NULL, '2025-07-18T13:29:46.249Z', NULL, NULL, NULL, '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"email_verified":true}', NULL, '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', NULL, NULL, '', '', NULL, NULL, 0, NULL, NULL, NULL, 'f', NULL, 'f');
INSERT INTO auth.identities (provider_id,user_id,identity_data,provider,last_sign_in_at,created_at,updated_at,id) VALUES ('e9274bac-ba72-5320-8d7d-ab1f9d0ee317', 'e9274bac-ba72-5320-8d7d-ab1f9d0ee317', '{"sub":"e9274bac-ba72-5320-8d7d-ab1f9d0ee317","email":"<EMAIL>","email_verified":true,"phone_verified":false}', 'email', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '0caa967c-2135-5dd3-ac50-a07e2a18a4ca');
INSERT INTO public.profile (id,first_name,last_name,gender,phone_number,organization_id,role,created_at,created_by,updated_at,updated_by) VALUES ('e9274bac-ba72-5320-8d7d-ab1f9d0ee317', 'Anja', 'Karus', 'male', '+492790810854', 'fabdb6a3-b8ba-5cc9-9616-f82e61dc5da5', 'tenant_manager', '2025-07-18T13:29:46.249Z', '9e54a714-95b6-5f5a-97e5-8d81b1107541', '2025-07-18T13:29:46.249Z', '9e54a714-95b6-5f5a-97e5-8d81b1107541');
INSERT INTO auth.users (instance_id,id,aud,role,email,encrypted_password,email_confirmed_at,invited_at,confirmation_token,confirmation_sent_at,recovery_token,recovery_sent_at,email_change_token_new,email_change,email_change_sent_at,last_sign_in_at,raw_app_meta_data,raw_user_meta_data,is_super_admin,created_at,updated_at,phone,phone_confirmed_at,phone_change,phone_change_token,phone_change_sent_at,email_change_token_current,email_change_confirm_status,banned_until,reauthentication_token,reauthentication_sent_at,is_sso_user,deleted_at,is_anonymous) VALUES ('00000000-0000-0000-0000-000000000000', '3eb602bd-025f-5b70-a608-ec025b48f316', 'authenticated', 'authenticated', '<EMAIL>', '', '2025-07-18T13:29:46.249Z', NULL, NULL, '2025-07-18T13:29:46.249Z', NULL, NULL, NULL, '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"email_verified":true}', NULL, '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', NULL, NULL, '', '', NULL, NULL, 0, NULL, NULL, NULL, 'f', NULL, 'f');
INSERT INTO auth.identities (provider_id,user_id,identity_data,provider,last_sign_in_at,created_at,updated_at,id) VALUES ('3eb602bd-025f-5b70-a608-ec025b48f316', '3eb602bd-025f-5b70-a608-ec025b48f316', '{"sub":"3eb602bd-025f-5b70-a608-ec025b48f316","email":"<EMAIL>","email_verified":true,"phone_verified":false}', 'email', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '9d7384cf-ddf4-5604-9230-51bb09a74a18');
INSERT INTO public.profile (id,first_name,last_name,gender,phone_number,organization_id,role,created_at,created_by,updated_at,updated_by) VALUES ('3eb602bd-025f-5b70-a608-ec025b48f316', 'Chiara', 'Kiessling', 'female', '+49479887868267', 'fabdb6a3-b8ba-5cc9-9616-f82e61dc5da5', 'tenant_seller', '2025-07-18T13:29:46.249Z', 'e9274bac-ba72-5320-8d7d-ab1f9d0ee317', '2025-07-18T13:29:46.249Z', 'e9274bac-ba72-5320-8d7d-ab1f9d0ee317');
INSERT INTO auth.users (instance_id,id,aud,role,email,encrypted_password,email_confirmed_at,invited_at,confirmation_token,confirmation_sent_at,recovery_token,recovery_sent_at,email_change_token_new,email_change,email_change_sent_at,last_sign_in_at,raw_app_meta_data,raw_user_meta_data,is_super_admin,created_at,updated_at,phone,phone_confirmed_at,phone_change,phone_change_token,phone_change_sent_at,email_change_token_current,email_change_confirm_status,banned_until,reauthentication_token,reauthentication_sent_at,is_sso_user,deleted_at,is_anonymous) VALUES ('00000000-0000-0000-0000-000000000000', '13fea722-da5e-506f-abaf-d28eb25424a4', 'authenticated', 'authenticated', '<EMAIL>', '', '2025-07-18T13:29:46.249Z', NULL, NULL, '2025-07-18T13:29:46.249Z', NULL, NULL, NULL, '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"email_verified":true}', NULL, '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', NULL, NULL, '', '', NULL, NULL, 0, NULL, NULL, NULL, 'f', NULL, 'f');
INSERT INTO auth.identities (provider_id,user_id,identity_data,provider,last_sign_in_at,created_at,updated_at,id) VALUES ('13fea722-da5e-506f-abaf-d28eb25424a4', '13fea722-da5e-506f-abaf-d28eb25424a4', '{"sub":"13fea722-da5e-506f-abaf-d28eb25424a4","email":"<EMAIL>","email_verified":true,"phone_verified":false}', 'email', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '78d25604-324e-5bde-b325-9b2e0ec92875');
INSERT INTO public.profile (id,first_name,last_name,gender,phone_number,organization_id,role,created_at,created_by,updated_at,updated_by) VALUES ('13fea722-da5e-506f-abaf-d28eb25424a4', 'Annabell', 'Erdmann', 'male', '+49693094686033', 'fabdb6a3-b8ba-5cc9-9616-f82e61dc5da5', 'tenant_purchaser', '2025-07-18T13:29:46.249Z', 'e9274bac-ba72-5320-8d7d-ab1f9d0ee317', '2025-07-18T13:29:46.249Z', 'e9274bac-ba72-5320-8d7d-ab1f9d0ee317');
INSERT INTO auth.users (instance_id,id,aud,role,email,encrypted_password,email_confirmed_at,invited_at,confirmation_token,confirmation_sent_at,recovery_token,recovery_sent_at,email_change_token_new,email_change,email_change_sent_at,last_sign_in_at,raw_app_meta_data,raw_user_meta_data,is_super_admin,created_at,updated_at,phone,phone_confirmed_at,phone_change,phone_change_token,phone_change_sent_at,email_change_token_current,email_change_confirm_status,banned_until,reauthentication_token,reauthentication_sent_at,is_sso_user,deleted_at,is_anonymous) VALUES ('00000000-0000-0000-0000-000000000000', '4fff1401-b270-53e7-afa3-3c6b615c113b', 'authenticated', 'authenticated', '<EMAIL>', '', '2025-07-18T13:29:46.249Z', NULL, NULL, '2025-07-18T13:29:46.249Z', NULL, NULL, NULL, '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"email_verified":true}', NULL, '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', NULL, NULL, '', '', NULL, NULL, 0, NULL, NULL, NULL, 'f', NULL, 'f');
INSERT INTO auth.identities (provider_id,user_id,identity_data,provider,last_sign_in_at,created_at,updated_at,id) VALUES ('4fff1401-b270-53e7-afa3-3c6b615c113b', '4fff1401-b270-53e7-afa3-3c6b615c113b', '{"sub":"4fff1401-b270-53e7-afa3-3c6b615c113b","email":"<EMAIL>","email_verified":true,"phone_verified":false}', 'email', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '71bd4e50-e14b-539d-b7fa-647a9fcf01c4');
INSERT INTO public.profile (id,first_name,last_name,gender,phone_number,organization_id,role,created_at,created_by,updated_at,updated_by) VALUES ('4fff1401-b270-53e7-afa3-3c6b615c113b', 'Enes', 'Hooss', 'other', '+496693830067', 'fabdb6a3-b8ba-5cc9-9616-f82e61dc5da5', 'tenant_compliance', '2025-07-18T13:29:46.249Z', 'e9274bac-ba72-5320-8d7d-ab1f9d0ee317', '2025-07-18T13:29:46.249Z', 'e9274bac-ba72-5320-8d7d-ab1f9d0ee317');
INSERT INTO auth.users (instance_id,id,aud,role,email,encrypted_password,email_confirmed_at,invited_at,confirmation_token,confirmation_sent_at,recovery_token,recovery_sent_at,email_change_token_new,email_change,email_change_sent_at,last_sign_in_at,raw_app_meta_data,raw_user_meta_data,is_super_admin,created_at,updated_at,phone,phone_confirmed_at,phone_change,phone_change_token,phone_change_sent_at,email_change_token_current,email_change_confirm_status,banned_until,reauthentication_token,reauthentication_sent_at,is_sso_user,deleted_at,is_anonymous) VALUES ('00000000-0000-0000-0000-000000000000', 'f7a08812-7f59-5eb5-8077-60664f2ff467', 'authenticated', 'authenticated', '<EMAIL>', '', '2025-07-18T13:29:46.249Z', NULL, NULL, '2025-07-18T13:29:46.249Z', NULL, NULL, NULL, '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"email_verified":true}', NULL, '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', NULL, NULL, '', '', NULL, NULL, 0, NULL, NULL, NULL, 'f', NULL, 'f');
INSERT INTO auth.identities (provider_id,user_id,identity_data,provider,last_sign_in_at,created_at,updated_at,id) VALUES ('f7a08812-7f59-5eb5-8077-60664f2ff467', 'f7a08812-7f59-5eb5-8077-60664f2ff467', '{"sub":"f7a08812-7f59-5eb5-8077-60664f2ff467","email":"<EMAIL>","email_verified":true,"phone_verified":false}', 'email', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '13861b05-10ee-500b-9d82-33c6794d0b74');
INSERT INTO public.profile (id,first_name,last_name,gender,phone_number,organization_id,role,created_at,created_by,updated_at,updated_by) VALUES ('f7a08812-7f59-5eb5-8077-60664f2ff467', 'Jolin', 'Kelm', 'other', '+49340252394240', 'fabdb6a3-b8ba-5cc9-9616-f82e61dc5da5', 'tenant_seller', '2025-07-18T13:29:46.249Z', 'e9274bac-ba72-5320-8d7d-ab1f9d0ee317', '2025-07-18T13:29:46.249Z', 'e9274bac-ba72-5320-8d7d-ab1f9d0ee317');
INSERT INTO auth.users (instance_id,id,aud,role,email,encrypted_password,email_confirmed_at,invited_at,confirmation_token,confirmation_sent_at,recovery_token,recovery_sent_at,email_change_token_new,email_change,email_change_sent_at,last_sign_in_at,raw_app_meta_data,raw_user_meta_data,is_super_admin,created_at,updated_at,phone,phone_confirmed_at,phone_change,phone_change_token,phone_change_sent_at,email_change_token_current,email_change_confirm_status,banned_until,reauthentication_token,reauthentication_sent_at,is_sso_user,deleted_at,is_anonymous) VALUES ('00000000-0000-0000-0000-000000000000', '7fb38db2-d7ca-5738-ba0b-859413101dcf', 'authenticated', 'authenticated', '<EMAIL>', '', '2025-07-18T13:29:46.249Z', NULL, NULL, '2025-07-18T13:29:46.249Z', NULL, NULL, NULL, '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"email_verified":true}', NULL, '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', NULL, NULL, '', '', NULL, NULL, 0, NULL, NULL, NULL, 'f', NULL, 'f');
INSERT INTO auth.identities (provider_id,user_id,identity_data,provider,last_sign_in_at,created_at,updated_at,id) VALUES ('7fb38db2-d7ca-5738-ba0b-859413101dcf', '7fb38db2-d7ca-5738-ba0b-859413101dcf', '{"sub":"7fb38db2-d7ca-5738-ba0b-859413101dcf","email":"<EMAIL>","email_verified":true,"phone_verified":false}', 'email', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', '2025-07-18T13:29:46.249Z', 'cc5b129a-b069-5d92-bc74-0ed8492741e3');
INSERT INTO public.profile (id,first_name,last_name,gender,phone_number,organization_id,role,created_at,created_by,updated_at,updated_by) VALUES ('7fb38db2-d7ca-5738-ba0b-859413101dcf', 'Ariana', 'Kaul', 'male', '+4919745305345', 'fabdb6a3-b8ba-5cc9-9616-f82e61dc5da5', 'tenant_purchaser', '2025-07-18T13:29:46.249Z', 'e9274bac-ba72-5320-8d7d-ab1f9d0ee317', '2025-07-18T13:29:46.249Z', 'e9274bac-ba72-5320-8d7d-ab1f9d0ee317');
UPDATE AUTH.USERS SET confirmation_token = '', recovery_token = '', email_change_token_current = '', email_change_token_new = '', reauthentication_token = '';
ALTER TABLE public.organization ENABLE TRIGGER audit_organization;
ALTER TABLE public.profile ENABLE TRIGGER audit_profile;
