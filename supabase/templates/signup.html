<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Sign-up Verification</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      margin: 0;
      padding: 20px;
      color: #333;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
      border: 1px solid #ddd;
      border-radius: 5px;
    }
    h1 {
      font-size: 24px;
      margin-top: 30px;
      margin-bottom: 30px;
    }
    .code-container {
      background-color: #f5f5f5;
      padding: 15px;
      margin: 25px 0;
      text-align: center;
      border-radius: 5px;
    }
    .code {
      font-family: monospace;
      font-size: 24px;
      letter-spacing: 2px;
      font-weight: bold;
    }
    .link {
      word-break: break-all;
      color: #0284c7;
      text-decoration: none;
    }
    .terms {
      margin: 25px 0;
      padding-top: 20px;
      border-top: 1px solid #eee;
    }
    .footer {
      margin-top: 40px;
      font-size: 14px;
      color: #666;
      border-top: 1px solid #eee;
      padding-top: 20px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Verify your email to sign up</h1>

    <p>We have received a sign-up attempt.</p>

    <p>To complete the sign-up process, enter the 6-digit code in the original window:</p>

    <div class="code-container">
      <div class="code">{{ .Token }}</div>
    </div>

    <div class="terms">
      <p>Please note that by completing your sign-up you are agreeing to our <a href="{{ .SiteURL }}/terms" class="link">Terms of Service</a> and <a href="{{ .SiteURL }}/privacy" class="link">Privacy Policy</a>.</p>
    </div>

    <div class="footer">
      <p>If you didn't attempt to sign up but received this email, please ignore this email. Don't share or forward the 6-digit code with anyone.</p>
    </div>
  </div>
</body>
</html>
